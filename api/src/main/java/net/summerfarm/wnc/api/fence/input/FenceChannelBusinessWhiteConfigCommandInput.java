package net.summerfarm.wnc.api.fence.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-10-12 16:29:33
 * @version 1.0
 *
 */
@Data
public class FenceChannelBusinessWhiteConfigCommandInput implements Serializable{
	/**
	 * 下单渠道类型 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
	 */
	@NotBlank(message = "下单渠道类型不能为空")
	private String orderChannelType;

	/**
	 * 作用域下的业务ID 0是全部
	 */
	@NotBlank(message = "作用域下的业务ID不能为空")
	private String scopeChannelBusinessId;


	/**
	 * 作用域下的业务名称
	 */
	@NotBlank(message = "作用域下的业务名称不能为空")
	private String scopeChannelBusinessName;

	/**
	 * 租户ID
	 */
	private Long tenantId;

}