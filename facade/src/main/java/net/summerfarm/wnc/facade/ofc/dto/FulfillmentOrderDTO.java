package net.summerfarm.wnc.facade.ofc.dto;

import lombok.Data;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description:履约单数据转换对象
 * date: 2023/8/31 15:56
 *
 * <AUTHOR>
 */
@Data
public class FulfillmentOrderDTO implements Serializable {

    private static final long serialVersionUID = 656213260316997716L;

    /**
     * 外部单号
     */
    private String outerOrderId;

    /**
     * 来源，200：鲜沐-订单，201：鲜沐-售后，202：鲜沐-样品，203：鲜沐-省心送，210：saas-订单，211：saas-售后
     */
    private Integer source;

    /**
     * 外部联系人ID
     */
    private String outerContactId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 外部客户号
     */
    private String outerClientId;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 履约确认时间
     */
    private LocalDateTime fulfillConfirmTime;


    /**
     * 原城配仓编号
     */
    private Integer oldStoreNo;

    /**
     * 新城配仓编号
     */
    private Integer newStoreNo;

    /**
     * 旧区域编号
     */
    private Integer oldAreaNo;

    /**
     * 新区域编号
     */
    private Integer newAreaNo;

    /**
     * 新围栏ID
     */
    private Integer newFenceId;

    /**
     * 旧围栏ID
     */
    private Integer oldFenceId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * poi
     */
    private String poi;

    /**
     * 旧区域ID
     */
    private Integer oldAdCodeMsgId;

    /**
     * 新区域ID
     */
    private Integer newAdCodeMsgId;
}
