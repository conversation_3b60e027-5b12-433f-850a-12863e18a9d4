package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 围栏变更执行记录枚举
 * date: 2025/8/28 16:55<br/>
 *
 * <AUTHOR> />
 */
public interface WncCityAreaChangeWarehouseRecordsEnums {

    @Getter
    @AllArgsConstructor
    enum ChangeStatus{
        WAIT(0, "等待生效"),
        EFFECTIVE(10, "正常生效中"),
        END(20, "已结束"),
        CANCEL(30, "已取消")
        ;
        private Integer value;
        private String content;

    }

    @Getter
    @AllArgsConstructor
    enum AreaDefinationType{
        NORMAL(10, "普通范围区域"),
        CUSTOM(20, "自定义范围区域"),
        ;
        private Integer value;
        private String content;

    }
}
