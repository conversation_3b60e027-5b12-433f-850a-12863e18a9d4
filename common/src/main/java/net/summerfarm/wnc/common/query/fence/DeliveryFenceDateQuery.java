package net.summerfarm.wnc.common.query.fence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.base.AbstractPageQuery;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/7 15:19<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryFenceDateQuery extends AbstractPageQuery {

    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 联系人id
     */
    private Long contactId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * poi
     */
    private String poi;

    /**
     * 想配送日期集合
     */
    private List<LocalDate> wantDeliveryTime;

    /**
     * 订单来源
     */
    private Integer orderSource;

    /**
     * 查询开始时间
     */
    private LocalDate queryBeginDate;
    /**
     * 查询结束时间
     */
    private LocalDate queryEndDate;

    /**
     * order source
     */
    private SourceEnum orderSourceEnum;

    /**
     * sku集合信息
     */
    private List<String> skus;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 是否加单 true 加单 false不加单
     */
    private Boolean addOrderFlag;

    /**
     * 无需备货  true不需要，false或者null需要
     */
    private Boolean noNeedToStockUp;


}
