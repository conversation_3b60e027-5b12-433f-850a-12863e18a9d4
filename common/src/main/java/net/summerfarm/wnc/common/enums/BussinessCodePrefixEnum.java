package net.summerfarm.wnc.common.enums;


import lombok.Getter;

/**
 * 业务逻辑层代码前缀枚举
 *
 * <AUTHOR>
 * @date 2023/04/04
 */
@Getter
public enum BussinessCodePrefixEnum {

    /**
     * 单据类型
     */
    CHANGE_BATCH_NO(1, "切仓批次号", "CBN"),
    ;

    private int code;

    private String desc;

    private String flag;

    BussinessCodePrefixEnum(int code, String desc, String flag) {
        this.code = code;
        this.desc = desc;
        this.flag = flag;
    }

    public static String getDesc(int code) {
        for (BussinessCodePrefixEnum entity : BussinessCodePrefixEnum.values()) {
            if (entity.getCode() == code) {
                return entity.getDesc();
            }
        }

        return "";
    }

    public static BussinessCodePrefixEnum getDocType(int code) {
        for (BussinessCodePrefixEnum entity : BussinessCodePrefixEnum.values()) {
            if (entity.getCode() == code) {
                return entity;
            }
        }
        return null;
    }


}
