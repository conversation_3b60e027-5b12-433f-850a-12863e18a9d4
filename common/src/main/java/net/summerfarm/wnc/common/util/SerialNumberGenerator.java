package net.summerfarm.wnc.common.util;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wnc.common.enums.BussinessCodePrefixEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class SerialNumberGenerator {

    private static final String dateFormaterStr4BatchCode = "yyyyMMdd";

    private static final int days = 30;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 创建任务单号
     *
     * @param docType
     * @return
     */
    public String createCode(BussinessCodePrefixEnum docType) {
        long currentTimeMillis = System.currentTimeMillis();
        log.info("createCode start {}", docType.getDesc());
        // 生成key
        String flag = docType.getFlag();
        Date date = new Date();
        String dateStr4redis = new SimpleDateFormat(dateFormaterStr4BatchCode).format(date);
        StringBuilder key = new StringBuilder(flag).append(dateStr4redis);

        // 生成value
        Long value = redisTemplate.opsForValue().increment(key.toString());
        redisTemplate.expire(key.toString(), days, TimeUnit.DAYS);
        String serialString = StringUtils.strFormat(value.toString(), 6);

        // 生成时间戳
        String dateStr4code = new SimpleDateFormat(dateFormaterStr4BatchCode).format(date);

        StringBuilder result = new StringBuilder(flag).append(dateStr4code).append(serialString);
        log.info("createCode end {}, result: {}, cost: {} ms", docType.getDesc(), result,
                System.currentTimeMillis() - currentTimeMillis);
        return result.toString();
    }


}
