package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.Objects;

/**
 * Description:围栏切仓任务明细枚举
 * date: 2023/8/24 11:27
 *
 * <AUTHOR>
 */
public interface FenceChangeTaskDetailEnums {

    /**
     * 切仓任务明细来源
     */
    @Getter
    @AllArgsConstructor
    enum Source implements Enum2Args {

        /**
         * 鲜沐-订单
         */
        XM_MALL(200, "鲜沐-订单"),
        /**
         * 鲜沐-售后
         */
        XM_AFTER_SALE(201, "鲜沐-售后"),
        /**
         * 鲜沐-样品
         */
        XM_SAMPLE_APPLY(202, "鲜沐-样品"),
        /**
         * 鲜沐-省心送
         */
        XM_MALL_TIMING(203, "鲜沐-省心送"),
        /**
         * saas-订单
         */
        SAAS_MALL(210, "saas-订单"),
        /**
         * saas-售后
         */
        SAAS_AFTER_SALE(211, "saas-售后"),
        /**
         * 外单干线、城配
         */
        OUTER_TRUNK_CITY(151, "外单干线-城配"),
        /**
         * POP-订单
         */
        POP_MALL(230, "POP商城"),
        /**
         * POP-售后
         */
        POP_AFTER_SALE(231, "POP售后"),
        ;
        private final Integer value;
        private final String content;

        /**
         * 根据值获取切仓任务明细来源枚举
         *
         * @param value 值
         * @return 切仓任务明细来源枚举
         */
        public static FenceChangeTaskDetailEnums.Source getSourceByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的切仓任务明细来源:" + value));
        }
    }

    /**
     * 切仓任务明细来源
     */
    @Getter
    @AllArgsConstructor
    enum Status implements Enum2Args {

        /**
         * 待处理
         */
        WAIT(10, "待处理"),
        /**
         * 处理成功
         */
        SUCCESS(20, "处理成功"),
        /**
         * 处理失败
         */
        FAIL(30, "处理失败"),
        /**
         * 无需处理
         */
        NO_NEED(40, "无需处理"),
        ;
        private final Integer value;
        private final String content;

        /**
         * 根据值获取切仓任务明细状态枚举
         *
         * @param value 值
         * @return 切仓任务明细状态枚举
         */
        public static FenceChangeTaskDetailEnums.Status getStatusByValue(Integer value) {
            return Arrays.stream(values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst()
                    .orElseThrow(() -> new BizException("不支持的切仓任务明细状态:" + value));
        }
    }
}
