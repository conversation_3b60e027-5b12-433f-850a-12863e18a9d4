<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceChangeRecordsMapper">
    <!-- 结果集映射 -->
    <resultMap id="wncFenceChangeRecordsResultMap" type="net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="fence_id" property="fenceId" jdbcType="INTEGER"/>
		<result column="fence_store_no" property="fenceStoreNo" jdbcType="INTEGER"/>
		<result column="fence_area_no" property="fenceAreaNo" jdbcType="INTEGER"/>
		<result column="fence_msg" property="fenceMsg" jdbcType="VARCHAR"/>
		<result column="change_batch_no" property="changeBatchNo" jdbcType="VARCHAR"/>
		<result column="fence_change_stage" property="fenceChangeStage" jdbcType="INTEGER"/>
		<result column="fence_change_task_id" property="fenceChangeTaskId" jdbcType="NUMERIC"/>
		<result column="fence_store_name" property="fenceStoreName" jdbcType="VARCHAR"/>
		<result column="fence_area_name" property="fenceAreaName" jdbcType="VARCHAR"/>
		<result column="fence_warehouse_nos" property="fenceWarehouseNos" jdbcType="VARCHAR"/>
		<result column="fence_warehouse_names" property="fenceWarehouseNames" jdbcType="VARCHAR"/>
		<result column="fence_name" property="fenceName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wncFenceChangeRecordsColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.fence_id,
          t.fence_store_no,
          t.fence_area_no,
          t.fence_msg,
          t.change_batch_no,
          t.fence_change_stage,
          t.fence_change_task_id,
          t.fence_store_name,
          t.fence_area_name,
          t.fence_warehouse_nos,
          t.fence_warehouse_names,
          t.fence_name
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="fenceId != null">
                AND t.fence_id = #{fenceId}
            </if>
			<if test="fenceStoreNo != null">
                AND t.fence_store_no = #{fenceStoreNo}
            </if>
			<if test="fenceAreaNo != null">
                AND t.fence_area_no = #{fenceAreaNo}
            </if>
			<if test="fenceMsg != null and fenceMsg !=''">
                AND t.fence_msg = #{fenceMsg}
            </if>
			<if test="changeBatchNo != null and changeBatchNo !=''">
                AND t.change_batch_no = #{changeBatchNo}
            </if>
			<if test="fenceChangeStage != null">
                AND t.fence_change_stage = #{fenceChangeStage}
            </if>
			<if test="fenceChangeTaskId != null">
                AND t.fence_change_task_id = #{fenceChangeTaskId}
            </if>
			<if test="fenceStoreName != null and fenceStoreName !=''">
                AND t.fence_store_name = #{fenceStoreName}
            </if>
			<if test="fenceAreaName != null and fenceAreaName !=''">
                AND t.fence_area_name = #{fenceAreaName}
            </if>
			<if test="fenceWarehouseNos != null and fenceWarehouseNos !=''">
                AND t.fence_warehouse_nos = #{fenceWarehouseNos}
            </if>
			<if test="fenceWarehouseNames != null and fenceWarehouseNames !=''">
                AND t.fence_warehouse_names = #{fenceWarehouseNames}
            </if>
			<if test="fenceName != null and fenceName !=''">
                AND t.fence_name = #{fenceName}
            </if>
            <if test="changeBatchNoList != null and changeBatchNoList.size() > 0">
                AND t.change_batch_no IN
                <foreach item="item" index="index" collection="changeBatchNoList"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="fenceId != null">
                    t.fence_id = #{fenceId},
                </if>
                <if test="fenceStoreNo != null">
                    t.fence_store_no = #{fenceStoreNo},
                </if>
                <if test="fenceAreaNo != null">
                    t.fence_area_no = #{fenceAreaNo},
                </if>
                <if test="fenceMsg != null">
                    t.fence_msg = #{fenceMsg},
                </if>
                <if test="changeBatchNo != null">
                    t.change_batch_no = #{changeBatchNo},
                </if>
                <if test="fenceChangeStage != null">
                    t.fence_change_stage = #{fenceChangeStage},
                </if>
                <if test="fenceChangeTaskId != null">
                    t.fence_change_task_id = #{fenceChangeTaskId},
                </if>
                <if test="fenceStoreName != null">
                    t.fence_store_name = #{fenceStoreName},
                </if>
                <if test="fenceAreaName != null">
                    t.fence_area_name = #{fenceAreaName},
                </if>
                <if test="fenceWarehouseNos != null">
                    t.fence_warehouse_nos = #{fenceWarehouseNos},
                </if>
                <if test="fenceWarehouseNames != null">
                    t.fence_warehouse_names = #{fenceWarehouseNames},
                </if>
                <if test="fenceName != null">
                    t.fence_name = #{fenceName},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wncFenceChangeRecordsResultMap" >
        SELECT <include refid="wncFenceChangeRecordsColumns" />
        FROM wnc_fence_change_records t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam"  resultType="net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.fence_id fenceId,
            t.fence_store_no fenceStoreNo,
            t.fence_area_no fenceAreaNo,
            t.fence_msg fenceMsg,
            t.change_batch_no changeBatchNo,
            t.fence_change_stage fenceChangeStage,
            t.fence_change_task_id fenceChangeTaskId,
            t.fence_store_name fenceStoreName,
            t.fence_area_name fenceAreaName,
            t.fence_warehouse_nos fenceWarehouseNos,
            t.fence_warehouse_names fenceWarehouseNames,
            t.fence_name fenceName
        FROM wnc_fence_change_records t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam" resultMap="wncFenceChangeRecordsResultMap" >
        SELECT <include refid="wncFenceChangeRecordsColumns" />
        FROM wnc_fence_change_records t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wnc_fence_change_records
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="fenceId != null">
				  fence_id,
              </if>
              <if test="fenceStoreNo != null">
				  fence_store_no,
              </if>
              <if test="fenceAreaNo != null">
				  fence_area_no,
              </if>
              <if test="fenceMsg != null">
				  fence_msg,
              </if>
              <if test="changeBatchNo != null">
				  change_batch_no,
              </if>
              <if test="fenceChangeStage != null">
				  fence_change_stage,
              </if>
              <if test="fenceChangeTaskId != null">
				  fence_change_task_id,
              </if>
              <if test="fenceStoreName != null">
				  fence_store_name,
              </if>
              <if test="fenceAreaName != null">
				  fence_area_name,
              </if>
              <if test="fenceWarehouseNos != null">
				  fence_warehouse_nos,
              </if>
              <if test="fenceWarehouseNames != null">
				  fence_warehouse_names,
              </if>
              <if test="fenceName != null">
				  fence_name,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="fenceId != null">
				#{fenceId,jdbcType=NUMERIC},
              </if>
              <if test="fenceStoreNo != null">
				#{fenceStoreNo,jdbcType=INTEGER},
              </if>
              <if test="fenceAreaNo != null">
				#{fenceAreaNo,jdbcType=INTEGER},
              </if>
              <if test="fenceMsg != null">
				#{fenceMsg,jdbcType=VARCHAR},
              </if>
              <if test="changeBatchNo != null">
				#{changeBatchNo,jdbcType=VARCHAR},
              </if>
              <if test="fenceChangeStage != null">
				#{fenceChangeStage,jdbcType=INTEGER},
              </if>
              <if test="fenceChangeTaskId != null">
				#{fenceChangeTaskId,jdbcType=NUMERIC},
              </if>
              <if test="fenceStoreName != null">
				#{fenceStoreName,jdbcType=VARCHAR},
              </if>
              <if test="fenceAreaName != null">
				#{fenceAreaName,jdbcType=VARCHAR},
              </if>
              <if test="fenceWarehouseNos != null">
				#{fenceWarehouseNos,jdbcType=VARCHAR},
              </if>
              <if test="fenceWarehouseNames != null">
				#{fenceWarehouseNames,jdbcType=VARCHAR},
              </if>
              <if test="fenceName != null">
				#{fenceName,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords" >
        UPDATE wnc_fence_change_records t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords" >
        DELETE FROM wnc_fence_change_records
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>

    <update id="updatePreExeTimeAndFenceChangeTaskIdByChangeBatchNo">
        UPDATE wnc_fence_change_records t
        set t.pre_exe_time = #{preExeTime,jdbcType=TIMESTAMP},
            t.fence_change_task_id = #{fenceChangeTaskId,jdbcType=NUMERIC}
        where
            t.change_batch_no = #{changeBatchNo,jdbcType=VARCHAR}
    </update>

    <update id="cancelFenceChangeTask">
        UPDATE wnc_fence_change_records
        SET fence_change_task_id = null
        WHERE fence_change_task_id = #{fenceChangeTaskId}
    </update>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO wnc_fence_change_records (
            create_time, update_time, fence_id, fence_name, fence_store_no, fence_area_no,
            fence_msg, change_batch_no, fence_change_stage, fence_change_task_id,
            fence_store_name, fence_area_name, fence_warehouse_nos, fence_warehouse_names
        ) VALUES
        <foreach collection="list" item="record" separator=",">
            (
                #{record.createTime,jdbcType=TIMESTAMP},
                #{record.updateTime,jdbcType=TIMESTAMP},
                #{record.fenceId,jdbcType=NUMERIC},
                #{record.fenceName,jdbcType=VARCHAR},
                #{record.fenceStoreNo,jdbcType=INTEGER},
                #{record.fenceAreaNo,jdbcType=INTEGER},
                #{record.fenceMsg,jdbcType=VARCHAR},
                #{record.changeBatchNo,jdbcType=VARCHAR},
                #{record.fenceChangeStage,jdbcType=INTEGER},
                #{record.fenceChangeTaskId,jdbcType=NUMERIC},
                #{record.fenceStoreName,jdbcType=VARCHAR},
                #{record.fenceAreaName,jdbcType=VARCHAR},
                #{record.fenceWarehouseNos,jdbcType=VARCHAR},
                #{record.fenceWarehouseNames,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

</mapper>