package net.summerfarm.wnc.infrastructure.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsQuery;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.infrastructure.converter.WarehouseLogisticsCenterConverter;
import net.summerfarm.wnc.infrastructure.mapper.WarehouseLogisticsCenterMapper;
import net.summerfarm.wnc.infrastructure.model.WarehouseLogisticsCenter;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-06-20
 **/
@Service
public class WarehouseLogisticsCenterRepositoryImpl implements WarehouseLogisticsCenterRepository {

	@Autowired
	private WarehouseLogisticsCenterMapper warehouseLogisticsCenterMapper;

	@Override
	public LocalTime selectCloseTime(Integer storeNo) {
		WarehouseLogisticsCenter center = warehouseLogisticsCenterMapper.selectByStoreNo(storeNo);
		if (!Objects.isNull(center) && !StringUtils.isEmpty(center.getCloseTime())) {
			String closeTime = center.getCloseTime();
			LocalTime closeLocalTime = LocalTime.parse(closeTime, DateTimeFormatter.ofPattern("HH:mm:ss"));
			return closeLocalTime;
		} else {
			return LocalTime.of(22, 0, 0);
		}
	}


	@Override
	public List<WarehouseLogisticsCenterEntity> queryListByStoreNos(List<Integer> storeNos) {
		if(CollectionUtils.isEmpty(storeNos)){
			return Collections.emptyList();
		}
		List<WarehouseLogisticsCenter> warehouseLogisticsCenters = warehouseLogisticsCenterMapper.selectList(new LambdaQueryWrapper<WarehouseLogisticsCenter>()
				.in(WarehouseLogisticsCenter::getStoreNo,storeNos)
		);

		return warehouseLogisticsCenters.stream().map(WarehouseLogisticsCenterConverter::do2Entity).collect(Collectors.toList());
	}

	@Override
	public PageInfo<WarehouseLogisticsCenterEntity> queryPageList(WarehouseLogisticsQuery warehouseLogisticsQuery) {
		PageInfo<WarehouseLogisticsCenterEntity> resultPageInfo = new PageInfo<WarehouseLogisticsCenterEntity>();

		PageHelper.startPage(warehouseLogisticsQuery.getPageIndex(), warehouseLogisticsQuery.getPageSize());
		List<WarehouseLogisticsCenter> logisticsCenters = warehouseLogisticsCenterMapper.queryPageList(warehouseLogisticsQuery);

		if(CollectionUtils.isEmpty(logisticsCenters)){
			return resultPageInfo;
		}
		PageInfo<WarehouseLogisticsCenter> pageInfo = PageInfoHelper.createPageInfo(logisticsCenters);
		BeanUtil.copyProperties(pageInfo, resultPageInfo);
		resultPageInfo.setList(logisticsCenters.stream().map(WarehouseLogisticsCenterConverter::do2Entity).collect(Collectors.toList()));

		return resultPageInfo;
	}

	@Override
	public WarehouseLogisticsCenterEntity queryByUk(Integer storeNo) {
		if(storeNo == null){
			return null;
		}
		WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsCenterMapper.selectOne(new LambdaQueryWrapper<WarehouseLogisticsCenter>()
				.eq(WarehouseLogisticsCenter::getStoreNo, storeNo));
		return WarehouseLogisticsCenterConverter.do2Entity(warehouseLogisticsCenter);
	}

	@Override
	public List<WarehouseLogisticsCenterEntity> queryListByStoreNosAndStatus(List<Integer> storeNos, Integer status) {
		if(CollectionUtils.isEmpty(storeNos)){
			throw new BizException("查询storeNos不能为空");
		}
		if(status == null){
			throw new BizException("查询status不能为空");
		}
		List<WarehouseLogisticsCenter> warehouseLogisticsCenters = warehouseLogisticsCenterMapper.selectList(new LambdaQueryWrapper<WarehouseLogisticsCenter>()
				.in(WarehouseLogisticsCenter::getStoreNo,storeNos)
				.eq(WarehouseLogisticsCenter::getStatus,status)
		);

		return warehouseLogisticsCenters.stream().map(WarehouseLogisticsCenterConverter::do2Entity).collect(Collectors.toList());
	}

	@Override
	public WarehouseLogisticsCenterEntity queryById(Integer id) {
		if(id == null){
			return null;
		}
		WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsCenterMapper.selectById(id);
		return WarehouseLogisticsCenterConverter.do2Entity(warehouseLogisticsCenter);
	}

	@Override
	public long countByNameAndId(String storeName, Integer id) {
		return warehouseLogisticsCenterMapper.selectCount(new LambdaQueryWrapper<WarehouseLogisticsCenter>()
				.eq(WarehouseLogisticsCenter::getStoreName,storeName)
				.ne(id != null ,WarehouseLogisticsCenter::getId, id)
		);
	}

	@Override
	public void updateById(WarehouseLogisticsCenterEntity entity) {
		if(entity == null){
			return;
		}
		WarehouseLogisticsCenter warehouseLogisticsCenter = WarehouseLogisticsCenterConverter.entity2Do(entity);
		warehouseLogisticsCenter.setUpdateTime(LocalDateTime.now());
		warehouseLogisticsCenterMapper.updateById(warehouseLogisticsCenter);
	}

	@Override
	public int selectMaxStoreNo() {
		WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsCenterMapper.selectOne(new LambdaQueryWrapper<WarehouseLogisticsCenter>()
				.orderByDesc(WarehouseLogisticsCenter::getStoreNo)
				.last("limit 1")
		);

		return warehouseLogisticsCenter == null ? 0 : warehouseLogisticsCenter.getStoreNo();
	}

	@Override
	public void save(WarehouseLogisticsCenterEntity entity) {
		if(entity == null){
			return;
		}
		WarehouseLogisticsCenter warehouseLogisticsCenter = WarehouseLogisticsCenterConverter.entity2Do(entity);
		warehouseLogisticsCenter.setCreateTime(LocalDateTime.now());
		warehouseLogisticsCenterMapper.insert(warehouseLogisticsCenter);
	}

	@Override
	public void cancelUpdateCloseTime(Integer storeNo) {
		warehouseLogisticsCenterMapper.cancelUpdateCloseTime(storeNo);
	}

	@Override
	public List<WarehouseLogisticsCenterEntity> queryList(WarehouseLogisticsQuery query) {
		List<WarehouseLogisticsCenter> logisticsCenters = warehouseLogisticsCenterMapper.queryPageList(query);
		return logisticsCenters.stream().map(WarehouseLogisticsCenterConverter::do2Entity).collect(Collectors.toList());
	}

	@Override
	public List<WarehouseLogisticsCenterEntity> queryLogisticsList(WarehouseLogisticsQuery query) {
		List<WarehouseLogisticsCenter> logisticsCenters = warehouseLogisticsCenterMapper.selectList(new LambdaQueryWrapper<WarehouseLogisticsCenter>()
				.like(StringUtils.isNotBlank(query.getStoreName()),WarehouseLogisticsCenter::getStoreName,query.getStoreName())
				.eq(query.getStatus() != null,WarehouseLogisticsCenter::getStatus,query.getStatus())
				.eq(query.getStoreNo() != null,WarehouseLogisticsCenter::getStoreNo,query.getStoreNo())
				.eq(query.getFulfillmentType() != null,WarehouseLogisticsCenter::getFulfillmentType,query.getFulfillmentType())
				.in(!CollectionUtils.isEmpty(query.getStoreNos()),WarehouseLogisticsCenter::getStoreNo,query.getStoreNos())
		);
		return logisticsCenters.stream().map(WarehouseLogisticsCenterConverter::do2Entity).collect(Collectors.toList());
	}

	@Override
	public Map<Integer, String> queryStoreNoToNameMapByStoreNos(List<Integer> storeNoList) {
		if (CollectionUtils.isEmpty(storeNoList)) {
			return Collections.emptyMap();
		}
		List<WarehouseLogisticsCenterEntity> warehouseLogisticsCenterEntities = this.queryListByStoreNos(storeNoList);
		if (!CollectionUtils.isEmpty(warehouseLogisticsCenterEntities)) {
			return warehouseLogisticsCenterEntities.stream().collect(Collectors.toMap(WarehouseLogisticsCenterEntity::getStoreNo, WarehouseLogisticsCenterEntity::getStoreName, (v1, v2) -> v1));
		}
		return Collections.emptyMap();
	}
}
