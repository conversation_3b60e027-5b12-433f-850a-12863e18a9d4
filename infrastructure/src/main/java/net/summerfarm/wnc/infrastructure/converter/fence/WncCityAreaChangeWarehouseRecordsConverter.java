package net.summerfarm.wnc.infrastructure.converter.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
public class WncCityAreaChangeWarehouseRecordsConverter {

    private WncCityAreaChangeWarehouseRecordsConverter() {
        // 无需实现
    }




    public static List<WncCityAreaChangeWarehouseRecordsEntity> toWncCityAreaChangeWarehouseRecordsEntityList(List<WncCityAreaChangeWarehouseRecords> wncCityAreaChangeWarehouseRecordsList) {
        if (wncCityAreaChangeWarehouseRecordsList == null) {
            return Collections.emptyList();
        }
        List<WncCityAreaChangeWarehouseRecordsEntity> wncCityAreaChangeWarehouseRecordsEntityList = new ArrayList<>();
        for (WncCityAreaChangeWarehouseRecords wncCityAreaChangeWarehouseRecords : wncCityAreaChangeWarehouseRecordsList) {
            wncCityAreaChangeWarehouseRecordsEntityList.add(toWncCityAreaChangeWarehouseRecordsEntity(wncCityAreaChangeWarehouseRecords));
        }
        return wncCityAreaChangeWarehouseRecordsEntityList;
}


    public static WncCityAreaChangeWarehouseRecordsEntity toWncCityAreaChangeWarehouseRecordsEntity(WncCityAreaChangeWarehouseRecords wncCityAreaChangeWarehouseRecords) {
        if (wncCityAreaChangeWarehouseRecords == null) {
             return null;
        }
        WncCityAreaChangeWarehouseRecordsEntity wncCityAreaChangeWarehouseRecordsEntity = new WncCityAreaChangeWarehouseRecordsEntity();
        wncCityAreaChangeWarehouseRecordsEntity.setId(wncCityAreaChangeWarehouseRecords.getId());
        wncCityAreaChangeWarehouseRecordsEntity.setCreateTime(wncCityAreaChangeWarehouseRecords.getCreateTime());
        wncCityAreaChangeWarehouseRecordsEntity.setUpdateTime(wncCityAreaChangeWarehouseRecords.getUpdateTime());
        wncCityAreaChangeWarehouseRecordsEntity.setArea(wncCityAreaChangeWarehouseRecords.getArea());
        wncCityAreaChangeWarehouseRecordsEntity.setCity(wncCityAreaChangeWarehouseRecords.getCity());
        wncCityAreaChangeWarehouseRecordsEntity.setChangeStatus(wncCityAreaChangeWarehouseRecords.getChangeStatus());
        wncCityAreaChangeWarehouseRecordsEntity.setPreExeTime(wncCityAreaChangeWarehouseRecords.getPreExeTime());
        wncCityAreaChangeWarehouseRecordsEntity.setChangeBatchNo(wncCityAreaChangeWarehouseRecords.getChangeBatchNo());
        wncCityAreaChangeWarehouseRecordsEntity.setFenceChangeTaskId(wncCityAreaChangeWarehouseRecords.getFenceChangeTaskId());
        wncCityAreaChangeWarehouseRecordsEntity.setEffectiveTime(wncCityAreaChangeWarehouseRecords.getEffectiveTime());
        wncCityAreaChangeWarehouseRecordsEntity.setOverTime(wncCityAreaChangeWarehouseRecords.getOverTime());
        wncCityAreaChangeWarehouseRecordsEntity.setAreaDefinationType(wncCityAreaChangeWarehouseRecords.getAreaDefinationType());
        wncCityAreaChangeWarehouseRecordsEntity.setProvince(wncCityAreaChangeWarehouseRecords.getProvince());
        return wncCityAreaChangeWarehouseRecordsEntity;
    }








    public static WncCityAreaChangeWarehouseRecords toWncCityAreaChangeWarehouseRecords(WncCityAreaChangeWarehouseRecordsCommandParam param) {
        if (param == null) {
            return null;
        }
        WncCityAreaChangeWarehouseRecords wncCityAreaChangeWarehouseRecords = new WncCityAreaChangeWarehouseRecords();
        wncCityAreaChangeWarehouseRecords.setId(param.getId());
        wncCityAreaChangeWarehouseRecords.setCreateTime(param.getCreateTime());
        wncCityAreaChangeWarehouseRecords.setUpdateTime(param.getUpdateTime());
        wncCityAreaChangeWarehouseRecords.setArea(param.getArea());
        wncCityAreaChangeWarehouseRecords.setCity(param.getCity());
        wncCityAreaChangeWarehouseRecords.setChangeStatus(param.getChangeStatus());
        wncCityAreaChangeWarehouseRecords.setPreExeTime(param.getPreExeTime());
        wncCityAreaChangeWarehouseRecords.setChangeBatchNo(param.getChangeBatchNo());
        wncCityAreaChangeWarehouseRecords.setFenceChangeTaskId(param.getFenceChangeTaskId());
        wncCityAreaChangeWarehouseRecords.setEffectiveTime(param.getEffectiveTime());
        wncCityAreaChangeWarehouseRecords.setOverTime(param.getOverTime());
        wncCityAreaChangeWarehouseRecords.setAreaDefinationType(param.getAreaDefinationType());
        wncCityAreaChangeWarehouseRecords.setProvince(param.getProvince());
        return wncCityAreaChangeWarehouseRecords;
    }
}
