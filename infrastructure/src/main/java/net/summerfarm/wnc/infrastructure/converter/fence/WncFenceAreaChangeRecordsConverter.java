package net.summerfarm.wnc.infrastructure.converter.fence;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Slf4j
public class WncFenceAreaChangeRecordsConverter {

    private WncFenceAreaChangeRecordsConverter() {
        // 无需实现
    }




    public static List<WncFenceAreaChangeRecordsEntity> toWncFenceAreaChangeRecordsEntityList(List<WncFenceAreaChangeRecords> wncFenceAreaChangeRecordsList) {
        if (wncFenceAreaChangeRecordsList == null) {
            return Collections.emptyList();
        }
        List<WncFenceAreaChangeRecordsEntity> wncFenceAreaChangeRecordsEntityList = new ArrayList<>();
        for (WncFenceAreaChangeRecords wncFenceAreaChangeRecords : wncFenceAreaChangeRecordsList) {
            wncFenceAreaChangeRecordsEntityList.add(toWncFenceAreaChangeRecordsEntity(wncFenceAreaChangeRecords));
        }
        return wncFenceAreaChangeRecordsEntityList;
}


    public static WncFenceAreaChangeRecordsEntity toWncFenceAreaChangeRecordsEntity(WncFenceAreaChangeRecords wncFenceAreaChangeRecords) {
        if (wncFenceAreaChangeRecords == null) {
             return null;
        }
        WncFenceAreaChangeRecordsEntity wncFenceAreaChangeRecordsEntity = new WncFenceAreaChangeRecordsEntity();
        wncFenceAreaChangeRecordsEntity.setId(wncFenceAreaChangeRecords.getId());
        wncFenceAreaChangeRecordsEntity.setCreateTime(wncFenceAreaChangeRecords.getCreateTime());
        wncFenceAreaChangeRecordsEntity.setUpdateTime(wncFenceAreaChangeRecords.getUpdateTime());
        wncFenceAreaChangeRecordsEntity.setFenceChangeId(wncFenceAreaChangeRecords.getFenceChangeId());
        wncFenceAreaChangeRecordsEntity.setFenceId(wncFenceAreaChangeRecords.getFenceId());
        wncFenceAreaChangeRecordsEntity.setCity(wncFenceAreaChangeRecords.getCity());
        wncFenceAreaChangeRecordsEntity.setArea(wncFenceAreaChangeRecords.getArea());
        wncFenceAreaChangeRecordsEntity.setAdCodeMsgId(wncFenceAreaChangeRecords.getAdCodeMsgId());
        wncFenceAreaChangeRecordsEntity.setCustomAreaName(wncFenceAreaChangeRecords.getCustomAreaName());
        if (!StringUtils.isEmpty(wncFenceAreaChangeRecords.getAdCodeMsgDetail())) {
            try {
                wncFenceAreaChangeRecordsEntity.setAdCodeMsgDetailEntity(JSON.parseObject(wncFenceAreaChangeRecords.getAdCodeMsgDetail(), AdCodeMsgEntity.class));
            } catch (Exception e) {
                log.error("解析围栏区域变更记录详情异常", e);
            }
        }else{
            wncFenceAreaChangeRecordsEntity.setAdCodeMsgDetailEntity(null);
        }
        wncFenceAreaChangeRecordsEntity.setGeoShape(wncFenceAreaChangeRecords.getGeoShape());
        wncFenceAreaChangeRecordsEntity.setFenceChangeStage(wncFenceAreaChangeRecords.getFenceChangeStage());
        wncFenceAreaChangeRecordsEntity.setAreaDrawType(wncFenceAreaChangeRecords.getAreaDrawType());
        return wncFenceAreaChangeRecordsEntity;
    }








    public static WncFenceAreaChangeRecords toWncFenceAreaChangeRecords(WncFenceAreaChangeRecordsCommandParam param) {
        if (param == null) {
            return null;
        }
        WncFenceAreaChangeRecords wncFenceAreaChangeRecords = new WncFenceAreaChangeRecords();
        wncFenceAreaChangeRecords.setId(param.getId());
        wncFenceAreaChangeRecords.setCreateTime(param.getCreateTime());
        wncFenceAreaChangeRecords.setUpdateTime(param.getUpdateTime());
        wncFenceAreaChangeRecords.setFenceChangeId(param.getFenceChangeId());
        wncFenceAreaChangeRecords.setFenceId(param.getFenceId());
        wncFenceAreaChangeRecords.setCity(param.getCity());
        wncFenceAreaChangeRecords.setArea(param.getArea());
        wncFenceAreaChangeRecords.setAdCodeMsgId(param.getAdCodeMsgId());
        wncFenceAreaChangeRecords.setCustomAreaName(param.getCustomAreaName());
        wncFenceAreaChangeRecords.setAdCodeMsgDetail(param.getAdCodeMsgDetail());
        wncFenceAreaChangeRecords.setGeoShape(param.getGeoShape());
        wncFenceAreaChangeRecords.setFenceChangeStage(param.getFenceChangeStage());
        wncFenceAreaChangeRecords.setAreaDrawType(param.getAreaDrawType());
        return wncFenceAreaChangeRecords;
    }
}
