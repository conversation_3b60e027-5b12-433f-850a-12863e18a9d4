package net.summerfarm.wnc.infrastructure.mapper;

import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery;
import net.summerfarm.wnc.infrastructure.model.WncFenceChangeTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 围栏切仓任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-250 15:25:57
 */
public interface WncFenceChangeTaskMapper extends BaseMapper<WncFenceChangeTask> {

    /**
     * 分页查询切仓任务列表
     * @param fenceChangeTaskPageQuery 查询
     * @return 结果
     */
    List<WncFenceChangeTask> queryPage(FenceChangeTaskPageQuery fenceChangeTaskPageQuery);

    /**
     * 根据切仓任务ID集合更新切仓任务状态
     *
     * @param fenceChangeTaskIds 切仓任务ID集合
     * @param sourceStatus       源状态
     * @param targetStatus       目标状态
     */
    void updateTargetStatusByIds(@Param("fenceChangeTaskIds") List<Long> fenceChangeTaskIds,
                                 @Param("sourceStatus") Integer sourceStatus,
                                 @Param("targetStatus") Integer targetStatus);
}
