package net.summerfarm.wnc.infrastructure.repository;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.AdCodeMessageEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.fence.AreaRepository;
import net.summerfarm.wnc.domain.fence.entity.AreaEntity;
import net.summerfarm.wnc.infrastructure.converter.AreaConverter;
import net.summerfarm.wnc.infrastructure.mapper.AdCodeMsgMapper;
import net.summerfarm.wnc.infrastructure.mapper.AreaMapper;
import net.summerfarm.wnc.infrastructure.mapper.FenceMapper;
import net.summerfarm.wnc.infrastructure.model.AdCodeMsg;
import net.summerfarm.wnc.infrastructure.model.Area;
import net.summerfarm.wnc.infrastructure.model.Fence;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/8/2 13:36<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class AreaRepositoryImpl implements AreaRepository {

    @Resource
    private AreaMapper areaMapper;
    @Resource
    private AdCodeMsgMapper adCodeMsgMapper;
    @Resource
    private AreaConverter areaConverter;
    @Resource
    private ConfigRepository configRepository;
    @Resource
    private FenceMapper fenceMapper;

    @Override
    public List<Integer> queryOpenAreaList() {
        List<Area> areas = areaMapper.selectList(new LambdaQueryWrapper<Area>().eq(Area::getStatus, 1));
        if (CollectionUtils.isEmpty(areas)) {
            return Collections.emptyList();
        }
        return areas.stream().map(Area::getAreaNo).collect(Collectors.toList());
    }


    @Override
    public AreaEntity queryByAddress(@NonNull String city, String area) {
        if (configRepository.queryNoAreaCity().contains(city)) {
            area = null;
        }
        AdCodeMsg adCodeMsg = adCodeMsgMapper.selectOne(new LambdaQueryWrapper<AdCodeMsg>()
                .eq(AdCodeMsg::getCity, city)
                .eq(StringUtils.isNotBlank(area), AdCodeMsg::getArea, area)
                .eq(AdCodeMsg::getStatus, AdCodeMessageEnums.Status.NORMAL.getValue())
                .last("limit 1")
        );
        if (adCodeMsg == null || adCodeMsg.getFenceId() == null) {
            return null;
        }
        Fence fence = fenceMapper.selectById(adCodeMsg.getFenceId());
        log.info("查询到的围栏信息为" + JSONObject.toJSONString(fence));
        if (Objects.isNull(fence) || Objects.equals(FenceEnums.Status.INVALID.getValue(), fence.getStatus())) {
            return null;
        }

        List<Area> areas = areaMapper.selectList(new LambdaQueryWrapper<Area>().eq(Area::getAreaNo, fence.getAreaNo()));
        if (CollectionUtils.isEmpty(areas)) {
            return null;
        }
        return areaConverter.doToEntity(areas.get(0));
    }

    @Override
    public AreaEntity queryByUk(Integer areaNo) {
        Area area = areaMapper.selectOne(new LambdaQueryWrapper<Area>().eq(Area::getAreaNo, areaNo));
        return areaConverter.doToEntity(area);
    }

    @Override
    public List<AreaEntity> queryByAreaNos(List<Integer> areaNos) {
        if(CollectionUtils.isEmpty(areaNos)){
            return null;
        }
        List<Area> areas = areaMapper.selectList(new LambdaQueryWrapper<Area>().in(Area::getAreaNo, areaNos));

        return areaConverter.doToEntityList(areas);
    }

    @Override
    public Map<Integer, String> queryAreaNoToNameMapByAreaNos(List<Integer> areaNos) {
        if (CollectionUtils.isEmpty(areaNos)) {
            return Collections.emptyMap();
        }
        List<AreaEntity> areaEntities = this.queryByAreaNos(areaNos);
        if (!CollectionUtils.isEmpty(areaEntities)) {
            return areaEntities.stream().collect(Collectors.toMap(AreaEntity::getAreaNo, AreaEntity::getAreaName, (oldData, newData) -> oldData));
        }

        return Collections.emptyMap();
    }
}
