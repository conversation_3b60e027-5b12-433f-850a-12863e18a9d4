package net.summerfarm.wnc.infrastructure.repository.fence;


import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam;
import net.summerfarm.wnc.infrastructure.converter.fence.WncFenceAreaChangeRecordsConverter;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceAreaChangeRecordsMapper;
import net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords;
import net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceChangeRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncFenceChangeRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncFenceChangeRecordsQueryRepositoryImpl implements WncFenceChangeRecordsQueryRepository {

    @Autowired
    private WncFenceChangeRecordsMapper wncFenceChangeRecordsMapper;
    @Autowired
    private WncFenceAreaChangeRecordsMapper wncFenceAreaChangeRecordsMapper;


    @Override
    public PageInfo<WncFenceChangeRecordsEntity> getPage(WncFenceChangeRecordsQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WncFenceChangeRecordsEntity> entities = wncFenceChangeRecordsMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WncFenceChangeRecordsEntity selectById(Long id) {
        return WncFenceChangeRecordsConverter.toWncFenceChangeRecordsEntity(wncFenceChangeRecordsMapper.selectById(id));
    }


    @Override
    public List<WncFenceChangeRecordsEntity> selectByCondition(WncFenceChangeRecordsQueryParam param) {
        return WncFenceChangeRecordsConverter.toWncFenceChangeRecordsEntityList(wncFenceChangeRecordsMapper.selectByCondition(param));
    }

    @Override
    public List<WncFenceChangeRecordsEntity> selectByChangeBatchNoList(List<String> changeBatchNoList) {
        if(CollectionUtils.isEmpty(changeBatchNoList)){
            return Collections.emptyList();
        }
        WncFenceChangeRecordsQueryParam param = new WncFenceChangeRecordsQueryParam();
        param.setChangeBatchNoList(changeBatchNoList);
        return this.selectByCondition(param);
    }

    @Override
    public List<WncFenceChangeRecordsEntity> selectWithAreaByChangeBatchNo(String changeBatchNo) {
        if (changeBatchNo == null) {
            return Collections.emptyList();
        }
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = this.selectByChangeBatchNoList(Collections.singletonList(changeBatchNo));
        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return Collections.emptyList();
        }

        WncFenceAreaChangeRecordsQueryParam param = new WncFenceAreaChangeRecordsQueryParam();
        param.setFenceChangeIds(fenceChangeRecords.stream().map(WncFenceChangeRecordsEntity::getId).collect(Collectors.toList()));
        List<WncFenceAreaChangeRecords> wncFenceAreaChangeRecords = wncFenceAreaChangeRecordsMapper.selectByCondition(param);
        List<WncFenceAreaChangeRecordsEntity> wncFenceAreaChangeRecordsEntityList = WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecordsEntityList(wncFenceAreaChangeRecords);

        // 根据围栏变更记录ID设置区域变更记录
        fenceChangeRecords.forEach(fenceChangeRecord -> {
            fenceChangeRecord.setAreaChangeRecords(wncFenceAreaChangeRecordsEntityList.stream()
                    .filter(record -> record.getFenceChangeId().equals(fenceChangeRecord.getId()))
                    .collect(Collectors.toList())
            );
        });

        return fenceChangeRecords;
    }

    @Override
    public List<WncFenceChangeRecordsEntity> selectWithAreaByFenceChangeId(Long fenceChangeId) {
        if (fenceChangeId == null) {
            return Collections.emptyList();
        }

        WncFenceChangeRecordsQueryParam fenceParam = new WncFenceChangeRecordsQueryParam();
        fenceParam.setFenceChangeTaskId(fenceChangeId);
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = this.selectByCondition(fenceParam);
        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return Collections.emptyList();
        }

        WncFenceAreaChangeRecordsQueryParam param = new WncFenceAreaChangeRecordsQueryParam();
        param.setFenceChangeIds(fenceChangeRecords.stream().map(WncFenceChangeRecordsEntity::getId).collect(Collectors.toList()));
        List<WncFenceAreaChangeRecords> wncFenceAreaChangeRecords = wncFenceAreaChangeRecordsMapper.selectByCondition(param);
        List<WncFenceAreaChangeRecordsEntity> wncFenceAreaChangeRecordsEntityList = WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecordsEntityList(wncFenceAreaChangeRecords);

        // 根据围栏变更记录ID设置区域变更记录
        fenceChangeRecords.forEach(fenceChangeRecord -> {
            fenceChangeRecord.setAreaChangeRecords(wncFenceAreaChangeRecordsEntityList.stream()
                    .filter(record -> record.getFenceChangeId().equals(fenceChangeRecord.getId()))
                    .collect(Collectors.toList())
            );
        });

        return fenceChangeRecords;
    }
}