package net.summerfarm.wnc.infrastructure.mapper.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Mapper
public interface WncFenceChangeRecordsMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WncFenceChangeRecords record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WncFenceChangeRecords record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WncFenceChangeRecords selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WncFenceChangeRecords> selectByCondition(WncFenceChangeRecordsQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<WncFenceChangeRecordsEntity> getPage(WncFenceChangeRecordsQueryParam param);

    /**
     * 根据变更批次号更新预约切仓时间及切仓任务ID
     * @param preExeTime 预约切仓时间
     * @param fenceChangeTaskId 切仓任务ID
     * @param changeBatchNo 变更批次号
     */
    void updatePreExeTimeAndFenceChangeTaskIdByChangeBatchNo(@Param("preExeTime") LocalDateTime preExeTime,
                                                             @Param("fenceChangeTaskId") Long fenceChangeTaskId,
                                                             @Param("changeBatchNo") String changeBatchNo);

    /**
     * 取消切仓任务
     * @param fenceChangeTaskId 切仓任务ID
     */
    void cancelFenceChangeTask(@Param("fenceChangeTaskId") Long fenceChangeTaskId);

    /**
     * @Describe: 批量更新非空的数据
     * @param records
     * @return
     */
    int batchUpdate(@Param("records") List<WncFenceChangeRecords> records);

    /**
     * @Describe: 批量插入数据
     * @param records
     * @return
     */
    int batchInsert(@Param("list") List<WncFenceChangeRecords> records);
}

