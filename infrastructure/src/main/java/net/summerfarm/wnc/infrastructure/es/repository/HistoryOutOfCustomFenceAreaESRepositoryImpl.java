package net.summerfarm.wnc.infrastructure.es.repository;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.HistoryOutOfCustomFenceAreaESRepository;
import net.summerfarm.wnc.domain.fence.entity.HistoryOutOfCustomFenceAreaEntity;
import net.summerfarm.wnc.infrastructure.es.converter.HistoryOutOfCustomFenceAreaDocConverter;
import net.summerfarm.wnc.infrastructure.es.document.HistoryOutOfCustomFenceAreaDoc;
import net.summerfarm.wnc.infrastructure.es.mapper.HistoryOutOfCustomFenceAreaMapper;
import net.xianmu.common.exception.BizException;
import org.elasticsearch.common.geo.ShapeRelation;
import org.elasticsearch.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description: 自定义围栏ES<br/>
 * date: 2023/12/6 15:04<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
@Deprecated
public class HistoryOutOfCustomFenceAreaESRepositoryImpl implements HistoryOutOfCustomFenceAreaESRepository {

    @Resource
    private HistoryOutOfCustomFenceAreaMapper customFenceAreaMapper;

    @Override
    public HistoryOutOfCustomFenceAreaEntity queryByAdCode(String adCode) {
        if(adCode == null){
            throw new BizException("区域编码不能为空");
        }
        HistoryOutOfCustomFenceAreaDoc customFenceAreaDoc = customFenceAreaMapper.selectOne(new LambdaEsQueryWrapper<HistoryOutOfCustomFenceAreaDoc>()
                .eq(HistoryOutOfCustomFenceAreaDoc::getAdCode, adCode));
        return HistoryOutOfCustomFenceAreaDocConverter.model2Entity(customFenceAreaDoc);
    }

    @Override
    public Set<String> queryAdCodeByAdCodes(Set<String> adCodeSet) {
        if(CollectionUtils.isEmpty(adCodeSet)){
            return Collections.emptySet();
        }
        List<HistoryOutOfCustomFenceAreaDoc> customFenceAreaDocs = customFenceAreaMapper.selectList(new LambdaEsQueryWrapper<HistoryOutOfCustomFenceAreaDoc>()
                .in(HistoryOutOfCustomFenceAreaDoc::getAdCode, adCodeSet)
                .select(HistoryOutOfCustomFenceAreaDoc::getAdCode));
        if(CollectionUtils.isEmpty(customFenceAreaDocs)){
            return Collections.emptySet();
        }
        return customFenceAreaDocs.stream().map(HistoryOutOfCustomFenceAreaDoc::getAdCode).collect(java.util.stream.Collectors.toSet());
    }

    @Override
    public void batchSave(List<HistoryOutOfCustomFenceAreaEntity> needSaveDataList) {
        if(CollectionUtils.isEmpty(needSaveDataList)){
            return;
        }
        List<HistoryOutOfCustomFenceAreaDoc> customFenceAreaDocs = needSaveDataList.stream().map(HistoryOutOfCustomFenceAreaDocConverter::entity2Model).collect(Collectors.toList());
        customFenceAreaMapper.insertBatch(customFenceAreaDocs);
    }

    @Override
    public void createCustomFenceAreaIndex() {
        customFenceAreaMapper.createIndex();
    }

    @Override
    public HistoryOutOfCustomFenceAreaEntity queryPoiFence(String poi) {
        String[] split = poi.split(",");
        List<HistoryOutOfCustomFenceAreaDoc> customFenceAreaDocs = customFenceAreaMapper.selectList(new LambdaEsQueryWrapper<HistoryOutOfCustomFenceAreaDoc>()
                .geoShape(HistoryOutOfCustomFenceAreaDoc::getGeoLocation, new Point(Double.parseDouble(split[0]), Double.parseDouble(split[1])),ShapeRelation.CONTAINS));
        if(CollectionUtils.isEmpty(customFenceAreaDocs)){
            return null;
        }
        return HistoryOutOfCustomFenceAreaDocConverter.model2Entity(customFenceAreaDocs.get(0));
    }

    @Override
    public void save(HistoryOutOfCustomFenceAreaEntity entity) {
        if(entity == null){
            return;
        }
        customFenceAreaMapper.insert(HistoryOutOfCustomFenceAreaDocConverter.entity2Model(entity));
    }
}
