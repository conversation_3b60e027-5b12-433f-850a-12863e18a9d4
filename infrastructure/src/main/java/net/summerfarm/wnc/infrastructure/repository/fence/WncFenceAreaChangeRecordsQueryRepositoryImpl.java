package net.summerfarm.wnc.infrastructure.repository.fence;


import net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceAreaChangeRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncFenceAreaChangeRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncFenceAreaChangeRecordsQueryRepositoryImpl implements WncFenceAreaChangeRecordsQueryRepository {

    @Autowired
    private WncFenceAreaChangeRecordsMapper wncFenceAreaChangeRecordsMapper;


    @Override
    public PageInfo<WncFenceAreaChangeRecordsEntity> getPage(WncFenceAreaChangeRecordsQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WncFenceAreaChangeRecordsEntity> entities = wncFenceAreaChangeRecordsMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WncFenceAreaChangeRecordsEntity selectById(Long id) {
        return WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecordsEntity(wncFenceAreaChangeRecordsMapper.selectById(id));
    }


    @Override
    public List<WncFenceAreaChangeRecordsEntity> selectByCondition(WncFenceAreaChangeRecordsQueryParam param) {
        return WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecordsEntityList(wncFenceAreaChangeRecordsMapper.selectByCondition(param));
    }

    @Override
    public List<WncFenceAreaChangeRecordsEntity> selectByFenceChangeIds(List<Long> fenceChangeIds) {
        if (CollectionUtils.isEmpty(fenceChangeIds)) {
            return Collections.emptyList();
        }
        WncFenceAreaChangeRecordsQueryParam param = new WncFenceAreaChangeRecordsQueryParam();
        param.setFenceChangeIds(fenceChangeIds);
        return WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecordsEntityList(wncFenceAreaChangeRecordsMapper.selectByCondition(param));
    }
}