package net.summerfarm.wnc.infrastructure.repository.fence;

import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncCityAreaChangeWarehouseRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncCityAreaChangeWarehouseRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncCityAreaChangeWarehouseRecordsCommandRepositoryImpl implements WncCityAreaChangeWarehouseRecordsCommandRepository {

    @Autowired
    private WncCityAreaChangeWarehouseRecordsMapper wncCityAreaChangeWarehouseRecordsMapper;
    @Override
    public WncCityAreaChangeWarehouseRecordsEntity insertSelective(WncCityAreaChangeWarehouseRecordsCommandParam param) {
        WncCityAreaChangeWarehouseRecords wncCityAreaChangeWarehouseRecords = WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecords(param);
        wncCityAreaChangeWarehouseRecordsMapper.insertSelective(wncCityAreaChangeWarehouseRecords);
        return WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecordsEntity(wncCityAreaChangeWarehouseRecords);
    }

    @Override
    public int updateSelectiveById(WncCityAreaChangeWarehouseRecordsCommandParam param){
        return wncCityAreaChangeWarehouseRecordsMapper.updateSelectiveById(WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecords(param));
    }


    @Override
    public int remove(Long id) {
        return wncCityAreaChangeWarehouseRecordsMapper.remove(id);
    }

    @Override
    public void batchUpdate(ArrayList<WncCityAreaChangeWarehouseRecordsCommandParam> params) {
        if (params == null || params.isEmpty()) {
            return;
        }

        // 将参数列表转换为模型列表
        ArrayList<WncCityAreaChangeWarehouseRecords> records = new ArrayList<>();
        for (WncCityAreaChangeWarehouseRecordsCommandParam param : params) {
            records.add(WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecords(param));
        }

        // 调用mapper的批量更新方法
        wncCityAreaChangeWarehouseRecordsMapper.batchUpdate(records);
    }

    @Override
    public int batchInsert(List<WncCityAreaChangeWarehouseRecordsCommandParam> params) {
        if (params == null || params.isEmpty()) {
            return 0;
        }

        // 将参数列表转换为模型列表
        List<WncCityAreaChangeWarehouseRecords> records = new ArrayList<>();
        for (WncCityAreaChangeWarehouseRecordsCommandParam param : params) {
            records.add(WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecords(param));
        }

        // 调用mapper的批量插入方法
        return wncCityAreaChangeWarehouseRecordsMapper.batchInsert(records);
    }

    @Override
    public void updateChangeStatusByFenceChangeTaskId(Long fenceChangeTaskId, Integer changeStatus) {
        if (fenceChangeTaskId == null || changeStatus == null) {
            return;
        }
        wncCityAreaChangeWarehouseRecordsMapper.updateChangeStatusByFenceChangeTaskId(fenceChangeTaskId, changeStatus);
    }

    @Override
    public void resetCustomAreaFenceChangeTask(Long fenceChangeTaskId) {
        if (fenceChangeTaskId == null) {
            return;
        }
        wncCityAreaChangeWarehouseRecordsMapper.resetCustomAreaFenceChangeTask(fenceChangeTaskId, WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue());
    }

    @Override
    public void updateChangeStatusByIds(List<Long> cityAreaChangeIds, Integer sourceStatus, Integer targetStatus) {
        if (CollectionUtils.isEmpty(cityAreaChangeIds) || sourceStatus == null || targetStatus == null) {
            return;
        }
        wncCityAreaChangeWarehouseRecordsMapper.updateChangeStatusByIds(cityAreaChangeIds, sourceStatus, targetStatus);
    }
}