package net.summerfarm.wnc.infrastructure.converter.fence;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Slf4j
public class WncFenceChangeRecordsConverter {

    private WncFenceChangeRecordsConverter() {
        // 无需实现
    }




    public static List<WncFenceChangeRecordsEntity> toWncFenceChangeRecordsEntityList(List<WncFenceChangeRecords> wncFenceChangeRecordsList) {
        if (wncFenceChangeRecordsList == null) {
            return Collections.emptyList();
        }
        List<WncFenceChangeRecordsEntity> wncFenceChangeRecordsEntityList = new ArrayList<>();
        for (WncFenceChangeRecords wncFenceChangeRecords : wncFenceChangeRecordsList) {
            wncFenceChangeRecordsEntityList.add(toWncFenceChangeRecordsEntity(wncFenceChangeRecords));
        }
        return wncFenceChangeRecordsEntityList;
}


    public static WncFenceChangeRecordsEntity toWncFenceChangeRecordsEntity(WncFenceChangeRecords wncFenceChangeRecords) {
        if (wncFenceChangeRecords == null) {
             return null;
        }
        WncFenceChangeRecordsEntity wncFenceChangeRecordsEntity = new WncFenceChangeRecordsEntity();
        wncFenceChangeRecordsEntity.setId(wncFenceChangeRecords.getId());
        wncFenceChangeRecordsEntity.setCreateTime(wncFenceChangeRecords.getCreateTime());
        wncFenceChangeRecordsEntity.setUpdateTime(wncFenceChangeRecords.getUpdateTime());
        wncFenceChangeRecordsEntity.setFenceId(wncFenceChangeRecords.getFenceId());
        wncFenceChangeRecordsEntity.setFenceStoreNo(wncFenceChangeRecords.getFenceStoreNo());
        wncFenceChangeRecordsEntity.setFenceAreaNo(wncFenceChangeRecords.getFenceAreaNo());
        if (!StringUtils.isEmpty(wncFenceChangeRecords.getFenceMsg())) {
            try {
                wncFenceChangeRecordsEntity.setFenceDetailEntity(JSON.parseObject(wncFenceChangeRecords.getFenceMsg(), FenceEntity.class));
            } catch (Exception e) {
                log.error("解析围栏区域变更记录详情异常", e);
            }
        }else{
            wncFenceChangeRecordsEntity.setFenceDetailEntity(null);
        }
        wncFenceChangeRecordsEntity.setChangeBatchNo(wncFenceChangeRecords.getChangeBatchNo());
        wncFenceChangeRecordsEntity.setFenceChangeStage(wncFenceChangeRecords.getFenceChangeStage());
        wncFenceChangeRecordsEntity.setFenceChangeTaskId(wncFenceChangeRecords.getFenceChangeTaskId());
        wncFenceChangeRecordsEntity.setFenceStoreName(wncFenceChangeRecords.getFenceStoreName());
        wncFenceChangeRecordsEntity.setFenceAreaName(wncFenceChangeRecords.getFenceAreaName());
        wncFenceChangeRecordsEntity.setFenceWarehouseNos(wncFenceChangeRecords.getFenceWarehouseNos());
        wncFenceChangeRecordsEntity.setFenceWarehouseNames(wncFenceChangeRecords.getFenceWarehouseNames());
        wncFenceChangeRecordsEntity.setFenceName(wncFenceChangeRecords.getFenceName());
        return wncFenceChangeRecordsEntity;
    }








    public static WncFenceChangeRecords toWncFenceChangeRecords(WncFenceChangeRecordsCommandParam param) {
        if (param == null) {
            return null;
        }
        WncFenceChangeRecords wncFenceChangeRecords = new WncFenceChangeRecords();
        wncFenceChangeRecords.setId(param.getId());
        wncFenceChangeRecords.setCreateTime(param.getCreateTime());
        wncFenceChangeRecords.setUpdateTime(param.getUpdateTime());
        wncFenceChangeRecords.setFenceId(param.getFenceId());
        wncFenceChangeRecords.setFenceStoreNo(param.getFenceStoreNo());
        wncFenceChangeRecords.setFenceAreaNo(param.getFenceAreaNo());
        wncFenceChangeRecords.setFenceMsg(param.getFenceMsg());
        wncFenceChangeRecords.setChangeBatchNo(param.getChangeBatchNo());
        wncFenceChangeRecords.setFenceChangeStage(param.getFenceChangeStage());
        wncFenceChangeRecords.setFenceChangeTaskId(param.getFenceChangeTaskId());
        wncFenceChangeRecords.setFenceStoreName(param.getFenceStoreName());
        wncFenceChangeRecords.setFenceAreaName(param.getFenceAreaName());
        wncFenceChangeRecords.setFenceWarehouseNos(param.getFenceWarehouseNos());
        wncFenceChangeRecords.setFenceWarehouseNames(param.getFenceWarehouseNames());
        wncFenceChangeRecords.setFenceName(param.getFenceName());
        return wncFenceChangeRecords;
    }
}
