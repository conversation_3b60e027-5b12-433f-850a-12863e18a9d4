package net.summerfarm.wnc.infrastructure.es.repository;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.infrastructure.es.document.CustomFenceArea;
import net.summerfarm.wnc.infrastructure.es.document.HistoryOutOfCustomFenceAreaDoc;
import net.summerfarm.wnc.infrastructure.es.mapper.CustomFenceAreaEsMapper;
import org.elasticsearch.common.geo.ShapeRelation;
import org.elasticsearch.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 自定义围栏区域
 * date: 2025/9/3 11:11<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class CustomFenceAreaEsQueryRepositoryImpl implements CustomFenceAreaEsQueryRepository {

    @Resource
    private CustomFenceAreaEsMapper customFenceAreaEsMapper;

    @Override
    public Integer matchEsByAdCodeMsgIdsWithPoi(List<Integer> adCodeMsgIds, String orderPoi) {
        if (adCodeMsgIds == null || adCodeMsgIds.isEmpty() || orderPoi == null) {
            return null;
        }
        String[] split = orderPoi.split(",");
        List<CustomFenceArea> customFenceAreas = customFenceAreaEsMapper.selectList(new LambdaEsQueryWrapper<CustomFenceArea>()
                .in(CustomFenceArea::getAdCodeMsgId, adCodeMsgIds)
                .geoShape(CustomFenceArea::getGeoShape, new Point(Double.parseDouble(split[0]), Double.parseDouble(split[1])), ShapeRelation.INTERSECTS)
                .select(CustomFenceArea::getAdCodeMsgId)
        );

        log.info("请求参数: adCodeMsgIds{},orderPoi:{}, 返回结果: {}", JSON.toJSONString(adCodeMsgIds), orderPoi, JSON.toJSONString(customFenceAreas));

        if (CollectionUtils.isEmpty(customFenceAreas)) {
            return null;
        }

        if (customFenceAreas.size() > 1) {
            log.error("\n匹配到多个自定义围栏区域, 请求参数: adCodeMsgIds{},orderPoi:{}, 返回结果: {}", JSON.toJSONString(adCodeMsgIds), orderPoi, JSON.toJSONString(customFenceAreas) + "\n");
        }

        String adCodeMsgId = customFenceAreas.get(0).getAdCodeMsgId();
        if (StringUtils.isEmpty(adCodeMsgId)) {
            return null;
        }

        return Integer.parseInt(adCodeMsgId);
    }
}
