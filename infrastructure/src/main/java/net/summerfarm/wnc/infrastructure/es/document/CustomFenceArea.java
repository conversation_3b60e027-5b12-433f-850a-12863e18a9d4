package net.summerfarm.wnc.infrastructure.es.document;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.IndexId;
import cn.easyes.annotation.IndexName;
import cn.easyes.annotation.rely.FieldType;
import lombok.Data;

import java.util.Date;

@Data
@IndexName(value = "custom_fence_area", keepGlobalPrefix = false)
public class CustomFenceArea {
    
    @IndexId
    private String id;
    
    @IndexField(value = "adCodeMsgId", fieldType = FieldType.KEYWORD)
    private String adCodeMsgId;
    
    @IndexField(value = "province", fieldType = FieldType.KEYWORD)
    private String province;
    
    @IndexField(value = "city", fieldType = FieldType.KEYWORD)
    private String city;
    
    @IndexField(value = "area", fieldType = FieldType.KEYWORD)
    private String area;

    // 使用专用的Polygon类型
    @IndexField(value = "geoShape", fieldType = FieldType.GEO_SHAPE)
    private GeoShape geoShape;
    
    @IndexField(value = "status", fieldType = FieldType.INTEGER)
    private Integer status;
    
    @IndexField(value = "createTime", fieldType = FieldType.DATE, 
               dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private Date createTime;
    
    @IndexField(value = "updateTime", fieldType = FieldType.DATE,
               dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private Date updateTime;


}