package net.summerfarm.wnc.infrastructure.mapper.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Mapper
public interface WncCityAreaChangeWarehouseRecordsMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WncCityAreaChangeWarehouseRecords record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WncCityAreaChangeWarehouseRecords record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WncCityAreaChangeWarehouseRecords selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WncCityAreaChangeWarehouseRecords> selectByCondition(WncCityAreaChangeWarehouseRecordsQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<WncCityAreaChangeWarehouseRecordsEntity> getPage(WncCityAreaChangeWarehouseRecordsQueryParam param);

    /**
     * @Describe: 批量更新非空的数据
     * @param records
     * @return
     */
    int batchUpdate(@Param("records") List<WncCityAreaChangeWarehouseRecords> records);

    /**
     * @Describe: 批量插入数据
     * @param records
     * @return
     */
    int batchInsert(@Param("list") List<WncCityAreaChangeWarehouseRecords> records);

    /**
     * 根据切仓任务ID更新城市区域切仓记录的状态
     * @param fenceChangeTaskId 切仓任务ID
     * @param changeStatus 状态值
     */
    void updateChangeStatusByFenceChangeTaskId(@Param("fenceChangeTaskId") Long fenceChangeTaskId,@Param("changeStatus") Integer changeStatus);

    /**
     * 重置自定义区域的切仓任务
     * @param fenceChangeTaskId 切仓任务ID
     */
    void resetCustomAreaFenceChangeTask(@Param("fenceChangeTaskId") Long fenceChangeTaskId,@Param("changeStatus") Integer changeStatus);

    /**
     * 查询可执行的切仓记录
     *
     * @param preExeTime         预约执行时间
     * @param changeStatus       变更状态
     * @param areaDefinationType
     * @return 切仓记录集合
     */
    List<WncCityAreaChangeWarehouseRecords> selectExecutableRecords(@Param("preExeTime")LocalDateTime preExeTime,
                                                                    @Param("changeStatus") Integer changeStatus,
                                                                    @Param("areaDefinationType")  Integer areaDefinationType);

    /**
     * 批量更新城市区域切仓记录状态
     * @param cityAreaChangeIds 城市区域切仓记录ID集合
     * @param sourceStatus 旧状态
     * @param targetStatus 新状态
     */
    void updateChangeStatusByIds(@Param("cityAreaChangeIds") List<Long> cityAreaChangeIds,
                                 @Param("sourceStatus") Integer sourceStatus,
                                 @Param("targetStatus") Integer targetStatus);
}

