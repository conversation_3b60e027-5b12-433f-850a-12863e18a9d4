package net.summerfarm.wnc.infrastructure.es.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;
import net.summerfarm.wnc.infrastructure.es.document.CustomFenceArea;
import net.summerfarm.wnc.infrastructure.es.document.GeoShape;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 转换类
 * date: 2025/9/3 14:53<br/>
 *
 * <AUTHOR> />
 */
public class CustomFenceAreaConverter {

    public static CustomFenceAreaEsEntity model2Entity(CustomFenceArea customFenceArea) {
        if (customFenceArea == null) {
            return null;
        }
        CustomFenceAreaEsEntity customFenceAreaEntity = new CustomFenceAreaEsEntity();
        customFenceAreaEntity.setId(customFenceArea.getId());
        customFenceAreaEntity.setAdCodeMsgId(customFenceArea.getAdCodeMsgId() != null ? Integer.parseInt(customFenceArea.getAdCodeMsgId()) : null);
        customFenceAreaEntity.setProvince(customFenceArea.getProvince());
        customFenceAreaEntity.setCity(customFenceArea.getCity());
        customFenceAreaEntity.setArea(customFenceArea.getArea());
        GeoShape geoShapeStr = customFenceArea.getGeoShape();
        if (geoShapeStr != null) {
            GeoShape geoShape = JSON.parseObject(String.valueOf(geoShapeStr), GeoShape.class);
            customFenceAreaEntity.setGeoShape(String.valueOf(geoShape.getCoordinates()));
        }
        customFenceAreaEntity.setStatus(customFenceArea.getStatus());
        return customFenceAreaEntity;
    }


    public static CustomFenceArea entity2Model(CustomFenceAreaEsEntity customFenceAreaEsEntity) {
        if (customFenceAreaEsEntity == null) {
            return null;
        }
        CustomFenceArea customFenceArea = new CustomFenceArea();

        customFenceArea.setId(customFenceAreaEsEntity.getId());
        customFenceArea.setAdCodeMsgId(String.valueOf(customFenceAreaEsEntity.getAdCodeMsgId()));
        customFenceArea.setProvince(customFenceAreaEsEntity.getProvince());
        customFenceArea.setCity(customFenceAreaEsEntity.getCity());
        customFenceArea.setArea(customFenceAreaEsEntity.getArea());

        String geoShapeStr = customFenceAreaEsEntity.getGeoShape();
        if (StringUtils.isNotBlank(geoShapeStr)) {
            customFenceArea.setGeoShape(convertToGeoShape(geoShapeStr));
        }

        customFenceArea.setStatus(customFenceAreaEsEntity.getStatus());

        return customFenceArea;
    }

    public static GeoShape convertToGeoShape(String geoJson) {
        try {
            Object parsed = JSON.parse(geoJson); // 先解析为通用对象

            GeoShape geoShape = new GeoShape();
            geoShape.setType("Polygon");
            List<List<List<Double>>> coordinates = new ArrayList<>();

            if (parsed instanceof JSONArray) {
                JSONArray rootArray = (JSONArray) parsed;

                // 情况1：单环多边形 [[x,y],[x,y],...]
                if (isPointArray(rootArray.get(0))) {
                    List<List<Double>> ring = parseRing(rootArray);
                    coordinates.add(ring);
                }
                // 情况2：多环多边形 [[[x,y],[x,y],...], [[x,y],[x,y],...]]
                else {
                    for (int i = 0; i < rootArray.size(); i++) {
                        JSONArray ringArray = rootArray.getJSONArray(i);
                        coordinates.add(parseRing(ringArray));
                    }
                }
            }

            geoShape.setCoordinates(coordinates);
            return geoShape;
        } catch (JSONException e) {
            throw new IllegalArgumentException("Invalid GeoJSON format: " + geoJson, e);
        }
    }

    // 判断是否是点坐标数组 [x,y]
    private static boolean isPointArray(Object obj) {
        if (obj instanceof JSONArray) {
            JSONArray arr = (JSONArray) obj;
            return arr.size() == 2 &&
                    arr.get(0) instanceof Number &&
                    arr.get(1) instanceof Number;
        }
        return false;
    }

    // 解析单个环 [[x,y],[x,y],...]
    private static List<List<Double>> parseRing(JSONArray ringArray) {
        List<List<Double>> ring = new ArrayList<>();
        for (int i = 0; i < ringArray.size(); i++) {
            JSONArray point = ringArray.getJSONArray(i);
            ring.add(Arrays.asList(point.getDouble(0), point.getDouble(1)));
        }
        return ring;
    }

}
