package net.summerfarm.wnc.infrastructure.repository.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceChangeRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncFenceChangeRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncFenceChangeRecordsCommandRepositoryImpl implements WncFenceChangeRecordsCommandRepository {

    @Autowired
    private WncFenceChangeRecordsMapper wncFenceChangeRecordsMapper;
    @Override
    public WncFenceChangeRecordsEntity insertSelective(WncFenceChangeRecordsCommandParam param) {
        WncFenceChangeRecords wncFenceChangeRecords = WncFenceChangeRecordsConverter.toWncFenceChangeRecords(param);
        wncFenceChangeRecordsMapper.insertSelective(wncFenceChangeRecords);
        return WncFenceChangeRecordsConverter.toWncFenceChangeRecordsEntity(wncFenceChangeRecords);
    }

    @Override
    public int updateSelectiveById(WncFenceChangeRecordsCommandParam param){
        return wncFenceChangeRecordsMapper.updateSelectiveById(WncFenceChangeRecordsConverter.toWncFenceChangeRecords(param));
    }


    @Override
    public int remove(Long id) {
        return wncFenceChangeRecordsMapper.remove(id);
    }

    @Override
    public void updatePreExeTimeAndFenceChangeTaskIdByChangeBatchNo(LocalDateTime preExeTime, Long fenceChangeTaskId, String changeBatchNo) {
        wncFenceChangeRecordsMapper.updatePreExeTimeAndFenceChangeTaskIdByChangeBatchNo(preExeTime, fenceChangeTaskId, changeBatchNo);
    }

    @Override
    public void cancelFenceChangeTask(Long fenceChangeTaskId) {
        if (fenceChangeTaskId == null){
            return;
        }
        wncFenceChangeRecordsMapper.cancelFenceChangeTask(fenceChangeTaskId);
    }

    @Override
    public int batchInsert(List<WncFenceChangeRecordsCommandParam> params) {
        if (params == null || params.isEmpty()) {
            return 0;
        }

        // 将参数列表转换为模型列表
        List<WncFenceChangeRecords> records = new ArrayList<>();
        for (WncFenceChangeRecordsCommandParam param : params) {
            records.add(WncFenceChangeRecordsConverter.toWncFenceChangeRecords(param));
        }

        // 调用mapper的批量插入方法
        return wncFenceChangeRecordsMapper.batchInsert(records);
    }
}