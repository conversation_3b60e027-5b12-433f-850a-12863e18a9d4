package net.summerfarm.wnc.application.service.changeTask;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.client.mq.msg.out.FenceChangeAreaHandleMsg;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 普通围栏切仓区域处理测试类
 * 主要测试优化后的 executeNormalFenceChangeAreaHandleByBatch 方法
 */
public class NormalFenceChangeAreaHandleTest {

    @InjectMocks
    private FenceChangTaskHandleService fenceChangTaskHandleService;

    @Mock
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testExecuteNormalFenceChangeAreaHandleByBatch_EmptyFenceChangeRecords() {
        // Given
        String changeBatchNo = "NORMAL_BATCH_001";
        List<WncCityAreaChangeWarehouseRecordsEntity> records = createMockRecords();
        
        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo))
                .thenReturn(Collections.emptyList());

        // When
        fenceChangTaskHandleService.executeNormalFenceChangeAreaHandleByBatch(changeBatchNo, records);

        // Then
        verify(wncFenceChangeRecordsQueryRepository).selectWithAreaByChangeBatchNo(changeBatchNo);
        // 验证当没有围栏变更记录时，方法能正常返回而不抛出异常
    }

    @Test
    void testExecuteNormalFenceChangeAreaHandleByBatch_EmptyAfterRecords() {
        // Given
        String changeBatchNo = "NORMAL_BATCH_002";
        List<WncCityAreaChangeWarehouseRecordsEntity> records = createMockRecords();
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = createMockBeforeFenceChangeRecords();
        
        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo))
                .thenReturn(fenceChangeRecords);

        // When
        fenceChangTaskHandleService.executeNormalFenceChangeAreaHandleByBatch(changeBatchNo, records);

        // Then
        verify(wncFenceChangeRecordsQueryRepository).selectWithAreaByChangeBatchNo(changeBatchNo);
        // 验证当没有AFTER阶段的记录时，方法能正常返回
    }

    @Test
    void testBuildNormalFenceChangeContext() throws Exception {
        // Given
        String changeBatchNo = "NORMAL_BATCH_003";
        List<WncCityAreaChangeWarehouseRecordsEntity> records = createMockRecords();
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = createMockNormalFenceChangeRecords();
        
        when(wncFenceChangeRecordsQueryRepository.selectWithAreaByChangeBatchNo(changeBatchNo))
                .thenReturn(fenceChangeRecords);

        // When - 使用反射调用私有方法进行测试
        Object context = ReflectionTestUtils.invokeMethod(
                fenceChangTaskHandleService, 
                "buildNormalFenceChangeContext", 
                changeBatchNo, 
                records
        );

        // Then
        // 验证上下文构建成功
        assertNotNull(context);
        verify(wncFenceChangeRecordsQueryRepository).selectWithAreaByChangeBatchNo(changeBatchNo);
    }

    @Test
    void testDetermineNormalFenceChangeType_NormalToNormal() throws Exception {
        // Given
        List<WncFenceChangeRecordsEntity> beforeRecords = createMockNormalBeforeFenceChangeRecords();

        // When - 使用反射调用私有方法
        Object changeType = ReflectionTestUtils.invokeMethod(
                fenceChangTaskHandleService,
                "determineNormalFenceChangeType",
                beforeRecords
        );

        // Then
        // 验证返回的是 NORMAL_TO_NORMAL 类型
        assertNotNull(changeType);
        assertEquals("NORMAL_TO_NORMAL", changeType.toString());
    }

    @Test
    void testDetermineNormalFenceChangeType_Unknown() throws Exception {
        // Given
        List<WncFenceChangeRecordsEntity> emptyBeforeRecords = Collections.emptyList();

        // When - 使用反射调用私有方法
        Object changeType = ReflectionTestUtils.invokeMethod(
                fenceChangTaskHandleService,
                "determineNormalFenceChangeType",
                emptyBeforeRecords
        );

        // Then
        // 验证返回的是 UNKNOWN 类型
        assertNotNull(changeType);
        assertEquals("UNKNOWN", changeType.toString());
    }

    @Test
    void testCompleteTaskWithoutChanges() throws Exception {
        // Given
        List<WncCityAreaChangeWarehouseRecordsEntity> records = createMockRecords();

        // When - 使用反射调用私有方法
        ReflectionTestUtils.invokeMethod(
                fenceChangTaskHandleService,
                "completeTaskWithoutChanges",
                records
        );

        // Then
        // 验证方法执行成功（没有异常抛出）
        // 在实际测试中，可以验证 fenceChangeTaskDomainService.updateFenceChangeTaskStatus 被调用
    }

    /**
     * 创建模拟的城市区域变更记录
     */
    private List<WncCityAreaChangeWarehouseRecordsEntity> createMockRecords() {
        WncCityAreaChangeWarehouseRecordsEntity record = new WncCityAreaChangeWarehouseRecordsEntity();
        record.setId(1L);
        record.setProvince("广东省");
        record.setCity("深圳市");
        record.setArea("南山区");
        record.setChangeBatchNo("NORMAL_BATCH_001");
        record.setFenceChangeTaskId(200L);
        
        return Arrays.asList(record);
    }

    /**
     * 创建模拟的围栏变更记录（仅BEFORE阶段）
     */
    private List<WncFenceChangeRecordsEntity> createMockBeforeFenceChangeRecords() {
        WncFenceChangeRecordsEntity beforeRecord = new WncFenceChangeRecordsEntity();
        beforeRecord.setId(1L);
        beforeRecord.setFenceChangeStage("BEFORE");
        beforeRecord.setFenceId(2001);
        
        return Arrays.asList(beforeRecord);
    }

    /**
     * 创建模拟的普通围栏变更记录（包含BEFORE和AFTER阶段）
     */
    private List<WncFenceChangeRecordsEntity> createMockNormalFenceChangeRecords() {
        WncFenceChangeRecordsEntity beforeRecord = new WncFenceChangeRecordsEntity();
        beforeRecord.setId(1L);
        beforeRecord.setFenceChangeStage("BEFORE");
        beforeRecord.setFenceId(2001);
        
        // 创建普通围栏实体
        FenceEntity normalFence = new FenceEntity();
        normalFence.setId(2001);
        normalFence.setType(FenceEnums.Type.NORMAL);
        beforeRecord.setFenceDetailEntity(normalFence);
        
        WncFenceChangeRecordsEntity afterRecord = new WncFenceChangeRecordsEntity();
        afterRecord.setId(2L);
        afterRecord.setFenceChangeStage("AFTER");
        afterRecord.setFenceId(2002);
        
        return Arrays.asList(beforeRecord, afterRecord);
    }

    /**
     * 创建模拟的普通围栏BEFORE记录（用于类型判断测试）
     */
    private List<WncFenceChangeRecordsEntity> createMockNormalBeforeFenceChangeRecords() {
        WncFenceChangeRecordsEntity beforeRecord = new WncFenceChangeRecordsEntity();
        beforeRecord.setId(1L);
        beforeRecord.setFenceChangeStage("BEFORE");
        beforeRecord.setFenceId(2001);
        
        // 创建普通围栏实体
        FenceEntity normalFence = new FenceEntity();
        normalFence.setId(2001);
        normalFence.setType(FenceEnums.Type.NORMAL);
        beforeRecord.setFenceDetailEntity(normalFence);
        
        return Arrays.asList(beforeRecord);
    }

    /**
     * 创建模拟的区域变更记录
     */
    private List<WncFenceAreaChangeRecordsEntity> createMockAreaChangeRecords() {
        WncFenceAreaChangeRecordsEntity areaChangeRecord = new WncFenceAreaChangeRecordsEntity();
        areaChangeRecord.setId(1L);
        areaChangeRecord.setFenceId(2002);
        areaChangeRecord.setAdCodeMsgId(3001);
        areaChangeRecord.setCity("深圳市");
        areaChangeRecord.setArea("南山区");
        
        return Arrays.asList(areaChangeRecord);
    }

    /**
     * 创建模拟的围栏切仓消息
     */
    private List<FenceChangeAreaHandleMsg> createMockFenceChangeMessages() {
        FenceChangeAreaHandleMsg msg = new FenceChangeAreaHandleMsg();
        msg.setFenceProvince("广东省");
        msg.setFenceCity("深圳市");
        msg.setFenceAreas(Arrays.asList("南山区"));
        msg.setOldStoreNo("OLD_STORE_001");
        msg.setNewStoreNo("NEW_STORE_001");
        msg.setOldAreaNo("OLD_AREA_001");
        msg.setNewAreaNo("NEW_AREA_001");
        
        return Arrays.asList(msg);
    }
}
