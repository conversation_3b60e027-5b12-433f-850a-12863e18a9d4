package net.summerfarm.wnc.application.service.changeTask;

import java.time.LocalDateTime;

/**
 * 围栏切仓操作服务类
 * date: 2025/8/28 16:34<br/>
 *
 * <AUTHOR> />
 */
public interface FenceChangeTaskCommandService {


    /**
     * 围栏区域预约切仓时间
     * @param preExeTime 预约切仓时间
     * @param cityAreaChangeWarehouseRecordId 城市区域变更记录ID
     */
    void preExeTime(LocalDateTime preExeTime, Long cityAreaChangeWarehouseRecordId);
}
