package net.summerfarm.wnc.application.service.changeTask.converter;

import net.summerfarm.wnc.api.changeTask.dto.FenceChangeRemarkDTO;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskDTO;
import net.summerfarm.wnc.api.changeTask.input.FenceChangeTaskCommand;
import net.summerfarm.wnc.api.fence.dto.FenceAreaDTO;
import net.summerfarm.wnc.application.fence.converter.FenceAreaConverter;
import net.summerfarm.wnc.application.service.changeTask.dto.CustomFenceChangeTaskDetailDTO;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.vo.FenceChangeRemarkVO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:围栏切仓任务转换器
 * date: 2023/8/25 17:38
 *
 * <AUTHOR>
 */
public class FenceChangeTaskConverter {

    public static FenceChangeTaskDTO entity2DTO(FenceChangeTaskEntity fenceChangeTaskEntity){
        if(fenceChangeTaskEntity == null){
            return null;
        }
        FenceChangeTaskDTO fenceChangeTaskDTO = new FenceChangeTaskDTO();
        fenceChangeTaskDTO.setId(fenceChangeTaskEntity.getId());
        fenceChangeTaskDTO.setFenceName(fenceChangeTaskEntity.getFenceName());
        fenceChangeTaskDTO.setFenceId(fenceChangeTaskEntity.getFenceId());
        fenceChangeTaskDTO.setType(fenceChangeTaskEntity.getType().getValue());
        fenceChangeTaskDTO.setTypeDesc(fenceChangeTaskEntity.getType().getContent());
        fenceChangeTaskDTO.setStatus(fenceChangeTaskEntity.getStatus().getValue());
        fenceChangeTaskDTO.setStatusDesc(fenceChangeTaskEntity.getStatus().getContent());
        fenceChangeTaskDTO.setAreaNo(fenceChangeTaskEntity.getAreaNo());
        fenceChangeTaskDTO.setTargetNo(fenceChangeTaskEntity.getTargetNo());

        if (Objects.equals(fenceChangeTaskEntity.getType(), FenceChangeTaskEnums.Type.CUSTOM)) {
            FenceAreaDTO fenceAreaDTO = new FenceAreaDTO();
            fenceAreaDTO.setCity(fenceChangeTaskEntity.getChangeCityName());
            fenceAreaDTO.setArea(fenceChangeTaskEntity.getChangeAreaName());

            fenceChangeTaskDTO.setFenceAreaDTOList(Collections.singletonList(fenceAreaDTO));
        } else {
            List<AdCodeMsgEntity> adCodeMsgEntities = fenceChangeTaskEntity.getAdCodeMsgEntities();
            if (!CollectionUtils.isEmpty(adCodeMsgEntities)) {
                fenceChangeTaskDTO.setFenceAreaDTOList(adCodeMsgEntities.stream().map(FenceAreaConverter::entity2dto).collect(Collectors.toList()));
            }
        }
        fenceChangeTaskDTO.setFenceChangeRemarkDTO(remarkVo2DTO(fenceChangeTaskEntity.getFenceChangeRemarkVO()));
        fenceChangeTaskDTO.setExeTime(fenceChangeTaskEntity.getExeTime());
        fenceChangeTaskDTO.setCreateTime(fenceChangeTaskEntity.getCreateTime());
        fenceChangeTaskDTO.setUpdateTime(fenceChangeTaskEntity.getUpdateTime());
        fenceChangeTaskDTO.setCreatorId(fenceChangeTaskEntity.getCreatorId());
        fenceChangeTaskDTO.setCreator(fenceChangeTaskEntity.getCreator());
        fenceChangeTaskDTO.setUpdater(fenceChangeTaskEntity.getUpdater());

        fenceChangeTaskDTO.setChangeCityName(fenceChangeTaskEntity.getChangeCityName());
        fenceChangeTaskDTO.setChangeAreaName(fenceChangeTaskEntity.getChangeAreaName());
        return fenceChangeTaskDTO;
    }

    public static FenceChangeRemarkDTO remarkVo2DTO(FenceChangeRemarkVO fenceChangeRemarkVO){
        if (fenceChangeRemarkVO == null){
            return null;
        }
        FenceChangeRemarkDTO fenceChangeRemarkDTO = new FenceChangeRemarkDTO();
        fenceChangeRemarkDTO.setOldWarehouses(fenceChangeRemarkVO.getOldWarehouses());
        fenceChangeRemarkDTO.setNewWarehouses(fenceChangeRemarkVO.getNewWarehouses());
        fenceChangeRemarkDTO.setOldArea(fenceChangeRemarkVO.getOldArea());
        fenceChangeRemarkDTO.setNewArea(fenceChangeRemarkVO.getNewArea());
        fenceChangeRemarkDTO.setOldStore(fenceChangeRemarkVO.getOldStore());
        fenceChangeRemarkDTO.setNewStore(fenceChangeRemarkVO.getNewStore());
        fenceChangeRemarkDTO.setExeTime(fenceChangeRemarkVO.getExeTime());
        return fenceChangeRemarkDTO;
    }

    public static FenceChangeTaskEntity command2Entity(FenceChangeTaskCommand fenceChangeTaskCommand){
        if(fenceChangeTaskCommand == null){
            return null;
        }
        FenceChangeTaskEntity fenceChangeTaskEntity = new FenceChangeTaskEntity();
        fenceChangeTaskEntity.setFenceId(fenceChangeTaskCommand.getFenceId());
        fenceChangeTaskEntity.setType(FenceChangeTaskEnums.Type.getTypeByValue(fenceChangeTaskCommand.getType()));
        fenceChangeTaskEntity.setTargetNo(fenceChangeTaskCommand.getTargetNo());
        List<AdCodeMsgEntity> adCodeMsgEntities = fenceChangeTaskCommand.getChangeAcmIds().stream().map(e -> {
            AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();
            adCodeMsgEntity.setId(e);
            return adCodeMsgEntity;
        }).collect(Collectors.toList());
        fenceChangeTaskEntity.setAdCodeMsgEntities(adCodeMsgEntities);
//        fenceChangeTaskEntity.setStatus();
//        fenceChangeTaskEntity.setAreaNo();
//        fenceChangeTaskEntity.setFenceChangeRemarkVO();
//        fenceChangeTaskEntity.setExeTime();
//        fenceChangeTaskEntity.setCreateTime();
//        fenceChangeTaskEntity.setUpdateTime();
//        fenceChangeTaskEntity.setOperator();
//        fenceChangeTaskEntity.setCreator();
        return fenceChangeTaskEntity;
    }

    /**
     * 构建自定义围栏切仓任务详情DTO
     */
    public static CustomFenceChangeTaskDetailDTO converterCustomFenceChangeTaskDetailDTO(
            FenceChangeTaskEntity fenceChangeTask,
            List<WncFenceChangeRecordsEntity> fenceChangeRecords) {

        CustomFenceChangeTaskDetailDTO result = new CustomFenceChangeTaskDetailDTO();

        // 设置基本信息
        result.setChangeAreaName(fenceChangeTask.getChangeAreaName());
        result.setChangeCityName(fenceChangeTask.getChangeCityName());
        if(fenceChangeTask.getType() != null){
            result.setType(fenceChangeTask.getType().getValue());
            result.setTypeDesc(fenceChangeTask.getType().getContent());
        }
        result.setCreateTime(fenceChangeTask.getCreateTime());

        // 分离变更前后的围栏变更记录
        Map<Integer, List<WncFenceChangeRecordsEntity>> beforeAfterMap = fenceChangeRecords.stream()
                .collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceChangeStage));

        List<WncFenceChangeRecordsEntity> beforeRecords = beforeAfterMap.getOrDefault(WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue(), Collections.emptyList());
        List<WncFenceChangeRecordsEntity> afterRecords = beforeAfterMap.getOrDefault(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue(), Collections.emptyList());

        // 构建变更前的数据
        result.setOldFenceAreaList(buildFenceAreaChangeTaskList(beforeRecords));
        result.setOldAreaGeoShapes(buildAreaGeoShapeList(beforeRecords));

        // 构建变更后的数据
        result.setNewFenceAreaList(buildFenceAreaChangeTaskList(afterRecords));
        result.setNewAreaGeoShapes(buildAreaGeoShapeList(afterRecords));

        return result;
    }

    /**
     * 构建围栏区域变更任务列表
     */
    private static List<CustomFenceChangeTaskDetailDTO.CustomFenceAreaChangeTaskDTO> buildFenceAreaChangeTaskList(
            List<WncFenceChangeRecordsEntity> fenceChangeRecords) {

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return Collections.emptyList();
        }

        // 按围栏ID分组
        Map<Integer, List<WncFenceChangeRecordsEntity>> fenceIdMap = fenceChangeRecords.stream()
                .collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceId));

        List<CustomFenceChangeTaskDetailDTO.CustomFenceAreaChangeTaskDTO> result = new ArrayList<>();

        for (Map.Entry<Integer, List<WncFenceChangeRecordsEntity>> entry : fenceIdMap.entrySet()) {
            List<WncFenceChangeRecordsEntity> records = entry.getValue();
            if (CollectionUtils.isEmpty(records)) {
                continue;
            }

            WncFenceChangeRecordsEntity firstRecord = records.get(0);
            CustomFenceChangeTaskDetailDTO.CustomFenceAreaChangeTaskDTO task =
                    new CustomFenceChangeTaskDetailDTO.CustomFenceAreaChangeTaskDTO();

            task.setFenceName(firstRecord.getFenceName());
            task.setStoreName(firstRecord.getFenceStoreName());
            task.setAreaNoName(firstRecord.getFenceAreaName());

            // 收集自定义区域名称
            Set<String> customAreaNames = records.stream()
                    .filter(record -> record.getAreaChangeRecords() != null)
                    .flatMap(record -> record.getAreaChangeRecords().stream())
                    .map(WncFenceAreaChangeRecordsEntity::getCustomAreaName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            task.setCustomAreaNameStrs(String.join(",", customAreaNames));

            // 收集仓库名称
            Set<String> warehouseNames = records.stream()
                    .map(WncFenceChangeRecordsEntity::getFenceWarehouseNames)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            task.setWarehouseNames(String.join(",", warehouseNames));

            result.add(task);
        }

        return result;
    }

    /**
     * 构建区域地理形状列表
     */
    private static List<CustomFenceChangeTaskDetailDTO.AreaGeoShapeDTO> buildAreaGeoShapeList(
            List<WncFenceChangeRecordsEntity> fenceChangeRecords) {

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return Collections.emptyList();
        }

        List<CustomFenceChangeTaskDetailDTO.AreaGeoShapeDTO> result = new ArrayList<>();

        for (WncFenceChangeRecordsEntity record : fenceChangeRecords) {
            if (record.getAreaChangeRecords() == null) {
                continue;
            }

            for (WncFenceAreaChangeRecordsEntity areaRecord : record.getAreaChangeRecords()) {
                CustomFenceChangeTaskDetailDTO.AreaGeoShapeDTO geoShape =
                        new CustomFenceChangeTaskDetailDTO.AreaGeoShapeDTO();

                geoShape.setCustomAreaName(areaRecord.getCustomAreaName());
                geoShape.setAreaDrawType(areaRecord.getAreaDrawType());
                geoShape.setGeoShape(areaRecord.getGeoShape());

                result.add(geoShape);
            }
        }

        return result;
    }
}
