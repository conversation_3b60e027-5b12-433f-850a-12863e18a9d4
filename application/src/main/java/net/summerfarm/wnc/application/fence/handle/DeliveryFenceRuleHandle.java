package net.summerfarm.wnc.application.fence.handle;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.fence.CalcFrenceFrequentDeliveryHelper;
import net.summerfarm.wnc.application.fence.converter.DeliveryRuleDTOConverter;
import net.summerfarm.wnc.application.fence.converter.StopDeliveryConverter;
import net.summerfarm.wnc.application.fence.dto.DeliveryRuleDTO;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.summerfarm.wnc.domain.fence.entity.DeliveryFenceEntity;
import net.summerfarm.wnc.domain.fence.entity.StopDeliveryEntity;
import net.summerfarm.wnc.domain.fence.handle.FenceChannelHandle;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 配送围栏规则处理器<br/>
 * date: 2024/3/7 17:57<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class DeliveryFenceRuleHandle{

    @Resource
    private WncConfig wncConfig;
    @Resource
    private  FenceChannelHandle fenceChannelHandle;
    @Resource
    private CalcFrenceFrequentDeliveryHelper calcFrenceFrequentDeliveryHelper;
    /**
     * 查询全品类的配送日期
     *
     * @param deliveryFenceEntity 配送围栏相关信息
     * @param startTime 首配日
     * @param sourceEnum 订单来源
     * @param orderTime 下单时间
     * @return 结果
     */
    public LocalDate queryFullCategoryDeliveryTime(DeliveryFenceEntity deliveryFenceEntity, LocalDate startTime, SourceEnum sourceEnum, LocalDateTime orderTime){
        //全品类停配日期
        LocalDate fullCategoryStopBeginDate = wncConfig.getFullCategoryStopBeginDate();
        LocalDate fullCategoryStopEndDate = wncConfig.getFullCategoryStopEndDate();

        // POP来源不设置全品类停配信息
        if (!SourceEnum.POP_MALL.equals(sourceEnum) && !SourceEnum.POP_AFTER_SALE.equals(sourceEnum)) {
            List<StopDeliveryEntity> stopDeliveryEntitys = deliveryFenceEntity.getStopDeliveryEntitys();

            if(fullCategoryStopBeginDate != null && fullCategoryStopEndDate != null && fullCategoryStopBeginDate.compareTo(fullCategoryStopEndDate) <= 0){
                StopDeliveryEntity stopDeliveryEntity = new StopDeliveryEntity();
                stopDeliveryEntity.setShutdownStartTime(fullCategoryStopBeginDate);
                stopDeliveryEntity.setShutdownEndTime(fullCategoryStopEndDate);

                if(CollectionUtils.isEmpty(stopDeliveryEntitys)){
                    stopDeliveryEntitys = Collections.singletonList(stopDeliveryEntity);
                }else{
                    stopDeliveryEntitys = new ArrayList<>(stopDeliveryEntitys);
                    stopDeliveryEntitys.add(stopDeliveryEntity);
                }
            }

            //设置全品类和城配仓停配信息
            deliveryFenceEntity.setStopDeliveryEntitys(stopDeliveryEntitys);
        }

        //截单日期的判断
        LocalDateTime closingTime = deliveryFenceEntity.buildOrderTimeCloseTime(orderTime);


        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextDay = now.plusDays(1);
        //订单时间是否过截单时间
        LocalDateTime compareTime = orderTime.compareTo(closingTime) <= 0 ? now : nextDay;
        long daysBetween = ChronoUnit.DAYS.between(compareTime.toLocalDate().atStartOfDay(), startTime.atStartOfDay());
        //全品类需要预留一天提货 所以需要大于等于2才行
        if (daysBetween < 2) {
            startTime = startTime.plusDays(1);
        }

        List<LocalDate> deliveryTimeList = this.getDeliveryTimeList(Collections.emptyList(), deliveryFenceEntity, startTime, sourceEnum);

        return CollectionUtils.isEmpty(deliveryTimeList) ? null : deliveryTimeList.get(0);
    };

    /**
     * 获取配送时间
     *
     * @param wantDeliveryTimeList 期望查询配送时间
     * @param deliveryFenceEntity  配送围栏
     * @param startTime    开始配送时间
     * @param sourceEnum 订单来源
     * @return 配送日期
     */
    public List<LocalDate> getDeliveryTimeList(List<LocalDate> wantDeliveryTimeList, DeliveryFenceEntity deliveryFenceEntity, LocalDate startTime, SourceEnum sourceEnum) {
        if (deliveryFenceEntity == null) {
            throw new BizException("此地区未开启配送");
        }
        // 围栏渠道校验
        boolean channelSupportFlag = fenceChannelHandle.channelCheck(deliveryFenceEntity, sourceEnum,deliveryFenceEntity.getFenceChannelBusinessWhiteConfigEntities());
        if(!channelSupportFlag){
            throw new BizException("未开放围栏渠道");
        }
        //判断是否支持配送
        ContactDeliveryRuleEntity contactDeliveryRuleEntity = deliveryFenceEntity.getContactDeliveryRuleEntity();
        if (deliveryFenceEntity.getFenceDeliveryEntity() == null) {
            throw new BizException("此地区未开启配送");
        }
        //客户配送周期为主，围栏为辅
        DeliveryRuleDTO deliveryRuleDTO;
        if(contactDeliveryRuleEntity != null){
            deliveryRuleDTO = DeliveryRuleDTOConverter.contactDelivery2DTO(contactDeliveryRuleEntity);
        }else{
            deliveryRuleDTO = DeliveryRuleDTOConverter.fenceDelivery2DTO(deliveryFenceEntity.getFenceDeliveryEntity());
        }
        List<StopDeliveryEntity> stopDeliveryEntitys = deliveryFenceEntity.getStopDeliveryEntitys();
        if(!CollectionUtils.isEmpty(stopDeliveryEntitys)){
            deliveryRuleDTO.setStopDeliveryDTOList(stopDeliveryEntitys.stream().map(StopDeliveryConverter::entity2DTO).collect(Collectors.toList()));
        }

        return calcFrenceFrequentDeliveryHelper.calcFenceFrequentDelivery(wantDeliveryTimeList, deliveryRuleDTO, startTime);
    }

    /**
     * 查询下一个的配送日期
     *
     * @param deliveryFenceEntity 配送围栏相关信息
     * @param startTime 首配日
     * @param sourceEnum 订单来源
     * @return 结果
     */
    public LocalDate queryNextDeliveryTime(DeliveryFenceEntity deliveryFenceEntity, LocalDate startTime, SourceEnum sourceEnum){
        startTime = startTime.plusDays(1);
        List<LocalDate> deliveryTimeList = this.getDeliveryTimeList(Collections.emptyList(), deliveryFenceEntity, startTime, sourceEnum);

        return CollectionUtils.isEmpty(deliveryTimeList) ? null : deliveryTimeList.get(0);
    };

}
