package net.summerfarm.wnc.application.service.changeTask.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.FenceChangeTaskQueryService;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskConverter;
import net.summerfarm.wnc.application.service.changeTask.dto.CustomFenceChangeTaskDetailDTO;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 围栏切仓查询服务类
 * date: 2025/8/28 16:34<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangeTaskQueryServiceImpl implements FenceChangeTaskQueryService {

    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    @Override
    public CustomFenceChangeTaskDetailDTO queryCustomFenceChangeTaskDetail(Long changeTaskId) {
        log.info("查询自定义围栏切仓任务详情，任务ID：{}", changeTaskId);

        // 1. 查询切仓任务基本信息
        FenceChangeTaskEntity fenceChangeTask = fenceChangeTaskRepository.queryById(changeTaskId);
        if (fenceChangeTask == null) {
            throw new BizException("切仓任务不存在");
        }

        // 2. 查询围栏变更记录（包含区域信息）
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(changeTaskId);

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            log.warn("未找到围栏变更记录，任务ID：{}", changeTaskId);
            return buildEmptyResult(fenceChangeTask);
        }

        // 3. 构建返回结果
        return FenceChangeTaskConverter.converterCustomFenceChangeTaskDetailDTO(fenceChangeTask, fenceChangeRecords);
    }

    /**
     * 构建空结果
     */
    private CustomFenceChangeTaskDetailDTO buildEmptyResult(FenceChangeTaskEntity fenceChangeTask) {
        CustomFenceChangeTaskDetailDTO result = new CustomFenceChangeTaskDetailDTO();
        result.setChangeAreaName(fenceChangeTask.getChangeAreaName());
        result.setChangeCityName(fenceChangeTask.getChangeCityName());
        if(fenceChangeTask.getType() != null){
            result.setType(fenceChangeTask.getType().getValue());
            result.setTypeDesc(fenceChangeTask.getType().getContent());
        }
        result.setCreateTime(fenceChangeTask.getCreateTime());
        result.setNewFenceAreaList(Collections.emptyList());
        result.setNewAreaGeoShapes(Collections.emptyList());
        result.setOldFenceAreaList(Collections.emptyList());
        result.setOldAreaGeoShapes(Collections.emptyList());
        return result;
    }

}
