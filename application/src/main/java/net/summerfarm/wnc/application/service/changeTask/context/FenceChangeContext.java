package net.summerfarm.wnc.application.service.changeTask.context;

import lombok.Builder;
import lombok.Data;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 围栏变更上下文
 */
@Data
@Builder
public class FenceChangeContext {
    private String changeBatchNo;

    private List<WncCityAreaChangeWarehouseRecordsEntity> records;

    private String province;

    private String city;

    private String area;

    private List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords;

    private List<WncFenceChangeRecordsEntity> afterFenceChangeRecords;

    public List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsGet() {
        if (CollectionUtils.isEmpty(this.beforeFenceChangeRecords)) {
            return Collections.emptyList();
        }
        
        return this.beforeFenceChangeRecords.stream()
                .map(WncFenceChangeRecordsEntity::getAreaChangeRecords)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream).collect(Collectors.toList());
    }

    public List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecordsGet() {
        if (CollectionUtils.isEmpty(this.afterFenceChangeRecords)) {
            return Collections.emptyList();
        }

        return this.afterFenceChangeRecords.stream()
                .map(WncFenceChangeRecordsEntity::getAreaChangeRecords)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream).collect(Collectors.toList());
    }
}