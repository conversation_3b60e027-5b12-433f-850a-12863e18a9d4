package net.summerfarm.wnc.application.inbound.controller.changeTask.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 自定义围栏切仓任务详情
 * date: 2025/9/9 11:02<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomFenceChangeTaskDetailVO {

    /**
     * 切换区域名称
     */
    private String changeAreaName;

    /**
     * 切换城市名称
     */
    private String changeCityName;

    /**
     * 类型,0:切仓,1:切围栏 2.自定义围栏切仓
     */
    private Integer type;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 新自定义区域集合
     */
    private List<CustomFenceAreaChangeTask> newFenceAreaList;

    /**
     * 新自定义区域坐标集合
     */
    private List<AreaGeoShape> newAreaGeoShapes;

    /**
     * 旧自定义区域集合
     */
    private List<CustomFenceAreaChangeTask> oldFenceAreaList;

    /**
     * 旧自定义区域坐标集合
     */
    private List<AreaGeoShape> oldAreaGeoShapes;



    @Data
    public static class CustomFenceAreaChangeTask{

        /**
         * 围栏名称
         */
        private String fenceName;

        /**
         * 城配仓名称
         */
        private String storeName;

        /**
         * 运营服务区名称
         */
        private String areaNoName;

        /**
         * 自定义区域名称，逗号分隔
         */
        private String customAreaNameStrs;

        /**
         * 仓库名称，逗号分隔
         */
        private String warehouseNames;
    }

    @Data
    public static class AreaGeoShape{

        /**
         * 围栏名称
         */
        private String customAreaName;

        /**
         * 区域绘制类型 0已绘制 1未绘制（其他区域）
         */
        private Integer areaDrawType;

        /**
         * 坐标POI图形
         */
        private String geoShape;

    }
}


