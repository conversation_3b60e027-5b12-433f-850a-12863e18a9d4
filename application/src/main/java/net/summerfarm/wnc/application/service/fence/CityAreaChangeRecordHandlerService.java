package net.summerfarm.wnc.application.service.fence;

import net.summerfarm.wnc.common.enums.BussinessCodePrefixEnum;
import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.common.util.SerialNumberGenerator;
import net.summerfarm.wnc.domain.fence.FenceDomainService;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.service.WncCityAreaChangeWarehouseRecordsCommandDomainService;
import net.summerfarm.wnc.domain.fence.service.WncFenceChangeRecordsCommandDomainService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 城市区域切仓变更记录处理服务
 * date: 2025/8/29 13:52<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class CityAreaChangeRecordHandlerService {

    @Resource
    private FenceRepository fenceRepository;
    @Resource
    private FenceDomainService fenceDomainService;
    @Resource
    private WncCityAreaChangeWarehouseRecordsCommandDomainService wncCityAreaChangeWarehouseRecordsCommandDomainService;
    @Resource
    private WncFenceChangeRecordsCommandDomainService wncFenceChangeRecordsCommandDomainService;
    @Resource
    private FenceChangeRecordFactory fenceChangeRecordFactory;
    @Resource
    private SerialNumberGenerator serialNumberGenerator;


    /**
     * 旧围栏切区域
     * @param fenceChangeTaskId 切仓任务ID
     * @param targetFenceId 目标围栏ID
     * @param sourceFenceId 源围栏ID
     * @param changeAcmIds 变更ACM集合
     * @param exeTime 执行时间
     */
    public void oldFenceAreaChangeFence(Long fenceChangeTaskId,
                                        Integer targetFenceId,
                                        Integer sourceFenceId,
                                        List<Integer> changeAcmIds,
                                        LocalDateTime exeTime) {

        // 根据围栏ID查询旧围栏和ACM记录，写入快照
        FenceEntity fenceEntity = fenceRepository.queryDetail(sourceFenceId);
        if (fenceEntity == null) {
            log.error("未找到源围栏详情数据, sourceFenceId: {}", sourceFenceId);
            return;
        }
        String changeBatchNo = serialNumberGenerator.createCode(BussinessCodePrefixEnum.CHANGE_BATCH_NO);

        // 创建城市区域、围栏变更记录
        this.createCityAreaWithFenceChangeHistoryRecord(fenceChangeTaskId, exeTime, fenceEntity, changeBatchNo, Arrays.asList(sourceFenceId, targetFenceId));

        // 2.保存变更后的围栏、区域记录
        this.createNotCustomFenceAfterChangeFenceAreaRecordForAreaChange(sourceFenceId,targetFenceId, changeBatchNo,fenceChangeTaskId, changeAcmIds);
    }

    /**
     * 旧围栏切城配仓
     * @param fenceChangeTaskId 切仓任务ID
     * @param targetStoreNo 目标城配仓编号
     * @param sourceFenceId 源围栏ID
     * @param exeTime 执行时间
     */
    public void oldFenceChangeStoreNo(Long fenceChangeTaskId,
                                      Integer targetStoreNo,
                                      Integer sourceFenceId,
                                      LocalDateTime exeTime) {
        // 根据围栏ID查询旧围栏和ACM记录，写入快照
        FenceEntity fenceEntity = fenceRepository.queryDetail(sourceFenceId);
        if (fenceEntity == null) {
            log.error("未找到源围栏详情数据, sourceFenceId: {}", sourceFenceId);
            return;
        }
        String changeBatchNo = serialNumberGenerator.createCode(BussinessCodePrefixEnum.CHANGE_BATCH_NO);

        // 1.创建变更记录
        this.createCityAreaWithFenceChangeHistoryRecord(fenceChangeTaskId, exeTime, fenceEntity, changeBatchNo, Collections.singletonList(sourceFenceId));

        // 2.保存变更后的围栏、区域记录(城配仓变更场景)
        this.createNotCustomFenceAfterChangeFenceAreaRecordForStoreNoChange(sourceFenceId, targetStoreNo, changeBatchNo, fenceChangeTaskId);
    }

    /**
     * 创建城市区域切仓记录和历史围栏区域记录
     */
    private void createCityAreaWithFenceChangeHistoryRecord(Long fenceChangeTaskId, LocalDateTime exeTime, FenceEntity fenceEntity, String changeBatchNo, List<Integer> fenceIds) {
        // 0.创建城市区域切仓记录
        this.createCityAreaChangeWarehouseRecords(fenceChangeTaskId, exeTime,
                fenceEntity, changeBatchNo,
                WncCityAreaChangeWarehouseRecordsEnums.AreaDefinationType.NORMAL.getValue());

        // 1.保存历史的围栏、区域信息
        this.createFenceAreaRecord(fenceIds, changeBatchNo, fenceChangeTaskId,WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE);
    }

    /**
     * 创建非自定义围栏类型变更后的围栏区域记录(城配仓变更围栏场景)
     */
    private void createNotCustomFenceAfterChangeFenceAreaRecordForStoreNoChange(Integer sourceFenceId,
                                                                                Integer targetStoreNo,
                                                                                 String changeBatchNo,
                                                                                Long fenceChangeTaskId) {
        if (sourceFenceId == null || targetStoreNo == null || changeBatchNo == null || fenceChangeTaskId == null) {
            return;
        }
        // 根据围栏ID查询围栏和ACM记录
        List<FenceEntity> fenceDetailEntityList = fenceDomainService.queryFenceAllDetailList(Collections.singletonList(sourceFenceId));

        if (CollectionUtils.isEmpty(fenceDetailEntityList)) {
            log.warn("未找到围栏详情数据, sourceFenceId: {}", sourceFenceId);
            return;
        }

        fenceDetailEntityList.forEach(fenceEntity -> {
            fenceEntity.setStoreNo(targetStoreNo);
        });

        List<WncFenceChangeRecordsCommandParam> fenceChangeRecordsCommandParams = fenceChangeRecordFactory.buildFenceAreaRecord(fenceDetailEntityList, changeBatchNo, fenceChangeTaskId, WncFenceChangeRecordsEnums.FenceChangeStage.AFTER);

        // 创建历史围栏区域记录
        wncFenceChangeRecordsCommandDomainService.createFenceAreaRecord(fenceChangeRecordsCommandParams);
    }


    /**
     * 创建非自定义围栏类型变更后的围栏区域记录(区域变更围栏场景)
     */
    private void createNotCustomFenceAfterChangeFenceAreaRecordForAreaChange(Integer sourceFenceId,
                                                                Integer targetFenceId,
                                                                String changeBatchNo,
                                                                Long fenceChangeTaskId,
                                                                List<Integer> changeAcmIds) {
        if (sourceFenceId == null || targetFenceId == null || changeBatchNo == null || fenceChangeTaskId == null || CollectionUtils.isEmpty(changeAcmIds)) {
            return;
        }
        // 根据围栏ID查询围栏和ACM记录
        List<FenceEntity> fenceDetailEntityList = fenceDomainService.queryFenceAllDetailList(Arrays.asList(sourceFenceId, targetFenceId));

        if (CollectionUtils.isEmpty(fenceDetailEntityList)) {
            log.warn("未找到围栏详情数据, sourceFenceId: {}, targetFenceId: {}", sourceFenceId, targetFenceId);
            return;
        }

        // 原围栏信息
        List<FenceEntity> sourceFenceEntityList = fenceDetailEntityList.stream().filter(fenceEntity -> Objects.equals(sourceFenceId, fenceEntity.getId())).collect(Collectors.toList());

        // 目标围栏信息
        List<FenceEntity> targetFenceEntityList = fenceDetailEntityList.stream().filter(fenceEntity -> Objects.equals(targetFenceId, fenceEntity.getId())).collect(Collectors.toList());

        List<AdCodeMsgEntity> sourceShouldHaveAdCodeMsgEntityList = sourceFenceEntityList.stream()
                .map(FenceEntity::getAdCodeMsgEntities)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(e -> changeAcmIds.contains(e.getId()))
                .collect(Collectors.toList());

        sourceFenceEntityList.get(0).setAdCodeMsgEntities(sourceShouldHaveAdCodeMsgEntityList);

        List<AdCodeMsgEntity> needChangeAdCodeMsgEntityList = sourceFenceEntityList.stream()
                .map(FenceEntity::getAdCodeMsgEntities)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(e -> changeAcmIds.contains(e.getId()))
                .collect(Collectors.toList());

        FenceEntity targetFenceEntity = targetFenceEntityList.get(0);
        List<AdCodeMsgEntity> targetFenceAdCodeMsgEntities = targetFenceEntity.getAdCodeMsgEntities();

        needChangeAdCodeMsgEntityList.forEach(adCodeMsgEntity -> {
            adCodeMsgEntity.setFenceId(targetFenceEntity.getId());
        });
        if (CollectionUtils.isEmpty(targetFenceAdCodeMsgEntities)) {
            targetFenceEntityList.get(0).setAdCodeMsgEntities(needChangeAdCodeMsgEntityList);
        } else {
            targetFenceAdCodeMsgEntities.addAll(needChangeAdCodeMsgEntityList);
        }

        List<WncFenceChangeRecordsCommandParam> fenceChangeRecordsCommandParams = fenceChangeRecordFactory.buildFenceAreaRecord(fenceDetailEntityList, changeBatchNo, fenceChangeTaskId, WncFenceChangeRecordsEnums.FenceChangeStage.AFTER);

        // 创建历史围栏区域记录
        wncFenceChangeRecordsCommandDomainService.createFenceAreaRecord(fenceChangeRecordsCommandParams);
    }

    /**
     * 创建城市区域切仓记录
     */
    private void createCityAreaChangeWarehouseRecords(Long fenceChangeTaskId,
                                                      LocalDateTime exeTime,
                                                      FenceEntity fenceEntity,
                                                      String changeBatchNo,
                                                      Integer areaDefinationType) {

        List<AdCodeMsgEntity> sourceFenceAdCodeMsgList = fenceEntity.getAdCodeMsgEntities();
        if(CollectionUtils.isEmpty(sourceFenceAdCodeMsgList)){
            throw new BizException("未找到源围栏区域数据");
        }
        List<WncCityAreaChangeWarehouseRecordsCommandParam> cityAreaChangeWarehouseRecordsList = new ArrayList<>();
        sourceFenceAdCodeMsgList.forEach(adCodeMsg ->{
            // 需要创建城市区域切仓记录
            WncCityAreaChangeWarehouseRecordsCommandParam cityAreaChangCommandParam = new WncCityAreaChangeWarehouseRecordsCommandParam();
            cityAreaChangCommandParam.setProvince(adCodeMsg.getProvince());
            cityAreaChangCommandParam.setCity(adCodeMsg.getCity());
            cityAreaChangCommandParam.setArea(adCodeMsg.getArea());
            cityAreaChangCommandParam.setChangeStatus(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue());
            cityAreaChangCommandParam.setPreExeTime(exeTime);
            cityAreaChangCommandParam.setChangeBatchNo(changeBatchNo);
            cityAreaChangCommandParam.setFenceChangeTaskId(fenceChangeTaskId);
            cityAreaChangCommandParam.setAreaDefinationType(areaDefinationType);
            cityAreaChangeWarehouseRecordsList.add(cityAreaChangCommandParam);
        });
        wncCityAreaChangeWarehouseRecordsCommandDomainService.batchInsert(cityAreaChangeWarehouseRecordsList);
    }

    /**
     * 创建围栏区域记录
     */
    public void createFenceAreaRecord(List<Integer> fenceIds, String changeBatchNo, Long fenceChangeTaskId, WncFenceChangeRecordsEnums.FenceChangeStage fenceChangeStage) {
        if (CollectionUtils.isEmpty(fenceIds)) {
            return;
        }

        // 根据围栏ID查询旧围栏和ACM记录，写入快照
        List<FenceEntity> fenceDetailEntityList = fenceDomainService.queryFenceAllDetailList(fenceIds);

        if (CollectionUtils.isEmpty(fenceDetailEntityList)) {
            log.warn("未找到围栏详情数据, fenceIds: {}", fenceIds);
            return;
        }

        List<WncFenceChangeRecordsCommandParam> fenceChangeRecordsCommandParams = fenceChangeRecordFactory.buildFenceAreaRecord(fenceDetailEntityList, changeBatchNo, fenceChangeTaskId, fenceChangeStage);

        // 创建历史围栏区域记录
        wncFenceChangeRecordsCommandDomainService.createFenceAreaRecord(fenceChangeRecordsCommandParams);
    }

}
