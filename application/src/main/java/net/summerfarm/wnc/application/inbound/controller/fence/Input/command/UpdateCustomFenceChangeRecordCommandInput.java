package net.summerfarm.wnc.application.inbound.controller.fence.Input.command;

import lombok.Data;
import net.summerfarm.wnc.api.fence.input.FenceChannelBusinessWhiteConfigCommandInput;
import net.summerfarm.wnc.api.fence.input.FenceDeliveryCommand;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 新增自定义围栏变更记录
 * date: 2025/8/27 10:31<br/>
 *
 * <AUTHOR> />
 */
@Data
public class UpdateCustomFenceChangeRecordCommandInput {

    /**
     * 自定义城市区域变更记录ID
     */
    @NotNull(message = "自定义城市区域变更记录ID不能为空")
    private Long cityAreaChangeWarehouseRecordId;

    /**
     * 围栏变更记录ID
     */
    @NotBlank(message = "围栏变更记录ID不能为空")
    private Long fenceChangeRecordsId;

    /**
     * 围栏名称
     */
    @NotBlank(message = "围栏名称不能为空")
    private String fenceName;

    /**
     * 城配仓编号
     */
    @NotNull(message = "城配仓编号不能为空")
    private Integer storeNo;

    /**
     * 城配仓名称
     */
    @NotBlank(message = "城配仓名称不能为空")
    private String storeName;

    /**
     * 运营服务区编号
     */
    @NotNull(message = "运营服务区编号不能为空")
    private Integer areaNo;

    /**
     * 运营服务区名称
     */
    @NotBlank(message = "运营服务区名称不能为空")
    private String areaName;

    /**
     * 类型，0：新建围栏，1：围栏拆分 2自定义围栏
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 配送周期相关
     */
    @Valid
    @NotNull(message = "围栏配送周期不能为空")
    private FenceDeliveryCommand fenceDeliveryCommand;

    /**
     * 下单渠道类型 逗号分隔  1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
     */
    @NotBlank(message = "下单渠道类型不能为空")
    private String orderChannelType;

    /**
     * 渠道白名单配置
     */
    @NotEmpty(message = "渠道白名单配置不能为空")
    @Valid
    private List<FenceChannelBusinessWhiteConfigCommandInput> fenceChannelBusinessWhiteConfigCommandInputList;

}
