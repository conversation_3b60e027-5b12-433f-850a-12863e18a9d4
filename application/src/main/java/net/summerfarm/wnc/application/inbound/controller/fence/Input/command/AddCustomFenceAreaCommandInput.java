package net.summerfarm.wnc.application.inbound.controller.fence.Input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增自定义区域
 * date: 2025/8/27 11:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class AddCustomFenceAreaCommandInput {

    /**
     * 自定义城市区域变更记录ID
     */
    @NotNull(message = "自定义城市区域变更记录ID不能为空")
    private Long cityAreaChangeWarehouseRecordId;

    /**
     * 围栏变更记录ID
     */
    @NotNull(message = "围栏变更记录ID不能为空")
    private Long fenceChangeRecordsId;

    /**
     * 坐标POI图形
     */
    @NotBlank(message = "区域等级不能为空")
    private String geoShape;

    /**
     * 自定义区域名称
     */
    @NotBlank(message = "自定义区域名称不能为空")
    private String customAreaName;

    /**
     * 区域绘制类型 0已绘制 1未绘制（其他区域）
     */
    @NotNull(message = "自定义区域名称不能为空")
    private Integer areaDrawType;
}
