package net.summerfarm.wnc.application.service.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaFenceDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaFenceAreaDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaFenceAreaDetailVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomFenceDetailVO;

/**
 * Description: <br/>
 * date: 2025/8/26 17:29<br/>
 *
 * <AUTHOR> />
 */
public interface CustomFenceQueryService {

    /**
     * 分页查询自定义区域列表
     * @param input
     * @return
     */
    PageInfo<CustomCityAreaVO> queryPage(CustomCityAreaQueryInput input);

    /**
     * 查询城市区域围栏区域详情
     * @param input
     * @return
     */
    CustomFenceDetailVO queryFenceAreaDetail(CustomCityAreaFenceDetailQueryInput input);

    /**
     * 查询自定义区域详情
     * @param input
     * @return
     */
    CustomCityAreaFenceAreaDetailVO queryCityAreaFenceAreaDetail(CustomCityAreaFenceAreaDetailQueryInput input);
}
