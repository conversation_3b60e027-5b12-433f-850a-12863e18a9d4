package net.summerfarm.wnc.application.inbound.controller.changeTask.converter;

import net.summerfarm.wnc.application.inbound.controller.changeTask.vo.CustomFenceChangeTaskDetailVO;
import net.summerfarm.wnc.application.service.changeTask.dto.CustomFenceChangeTaskDetailDTO;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 围栏切仓任务详情转换器
 * date: 2025/8/28 16:34<br/>
 *
 * <AUTHOR> />
 */
public class FenceChangeTaskVOConverter {

    /**
     * DTO转换为VO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    public static CustomFenceChangeTaskDetailVO toCustomFenceChangeTaskDetailVO(CustomFenceChangeTaskDetailDTO dto) {
        if (dto == null) {
            return null;
        }

        CustomFenceChangeTaskDetailVO vo = new CustomFenceChangeTaskDetailVO();
        vo.setChangeAreaName(dto.getChangeAreaName());
        vo.setChangeCityName(dto.getChangeCityName());
        vo.setType(dto.getType());
        vo.setTypeDesc(dto.getTypeDesc());
        vo.setCreateTime(dto.getCreateTime());

        // 转换变更前的围栏区域列表
        vo.setOldFenceAreaList(convertFenceAreaChangeTaskList(dto.getOldFenceAreaList()));

        // 转换变更前的区域地理形状列表
        vo.setOldAreaGeoShapes(convertAreaGeoShapeList(dto.getOldAreaGeoShapes()));

        // 转换变更后的围栏区域列表
        vo.setNewFenceAreaList(convertFenceAreaChangeTaskList(dto.getNewFenceAreaList()));

        // 转换变更后的区域地理形状列表
        vo.setNewAreaGeoShapes(convertAreaGeoShapeList(dto.getNewAreaGeoShapes()));

        return vo;
    }

    /**
     * 转换围栏区域变更任务列表
     *
     * @param dtoList DTO列表
     * @return VO列表
     */
    private static List<CustomFenceChangeTaskDetailVO.CustomFenceAreaChangeTask> convertFenceAreaChangeTaskList(
            List<CustomFenceChangeTaskDetailDTO.CustomFenceAreaChangeTaskDTO> dtoList) {

        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }

        return dtoList.stream()
                .map(FenceChangeTaskVOConverter::convertFenceAreaChangeTask)
                .collect(Collectors.toList());
    }

    /**
     * 转换围栏区域变更任务
     *
     * @param dto DTO对象
     * @return VO对象
     */
    private static CustomFenceChangeTaskDetailVO.CustomFenceAreaChangeTask convertFenceAreaChangeTask(
            CustomFenceChangeTaskDetailDTO.CustomFenceAreaChangeTaskDTO dto) {

        if (dto == null) {
            return null;
        }

        CustomFenceChangeTaskDetailVO.CustomFenceAreaChangeTask vo =
                new CustomFenceChangeTaskDetailVO.CustomFenceAreaChangeTask();

        vo.setFenceName(dto.getFenceName());
        vo.setStoreName(dto.getStoreName());
        vo.setAreaNoName(dto.getAreaNoName());
        vo.setCustomAreaNameStrs(dto.getCustomAreaNameStrs());
        vo.setWarehouseNames(dto.getWarehouseNames());

        return vo;
    }

    /**
     * 转换区域地理形状列表
     *
     * @param dtoList DTO列表
     * @return VO列表
     */
    private static List<CustomFenceChangeTaskDetailVO.AreaGeoShape> convertAreaGeoShapeList(
            List<CustomFenceChangeTaskDetailDTO.AreaGeoShapeDTO> dtoList) {

        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }

        return dtoList.stream()
                .map(FenceChangeTaskVOConverter::convertAreaGeoShape)
                .collect(Collectors.toList());
    }

    /**
     * 转换区域地理形状
     *
     * @param dto DTO对象
     * @return VO对象
     */
    private static CustomFenceChangeTaskDetailVO.AreaGeoShape convertAreaGeoShape(
            CustomFenceChangeTaskDetailDTO.AreaGeoShapeDTO dto) {

        if (dto == null) {
            return null;
        }

        CustomFenceChangeTaskDetailVO.AreaGeoShape vo =
                new CustomFenceChangeTaskDetailVO.AreaGeoShape();

        vo.setCustomAreaName(dto.getCustomAreaName());
        vo.setAreaDrawType(dto.getAreaDrawType());
        vo.setGeoShape(dto.getGeoShape());

        return vo;
    }
}
