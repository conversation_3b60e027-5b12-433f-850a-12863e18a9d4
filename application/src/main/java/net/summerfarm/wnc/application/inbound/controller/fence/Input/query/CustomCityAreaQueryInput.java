package net.summerfarm.wnc.application.inbound.controller.fence.Input.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 自定义区域分页查询
 * date: 2025/8/26 17:22<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomCityAreaQueryInput extends BasePageInput {

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 运营服务区编号
     */
    private Integer areaNo;

    /**
     * 自定义城市区域变更状态 0 等待生效 10正常生效中 20已结束
     */
    @NotNull(message = "状态不能为空")
    private List<Integer> cityAreaChangeStatusList;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空")
    private String city;

    /**
     * 区域
     */
    private List<String> areaList;
}
