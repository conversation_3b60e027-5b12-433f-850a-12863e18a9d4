package net.summerfarm.wnc.application.inbound.controller.fence.Input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增自定义区域
 * date: 2025/8/27 11:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class UpdateCustomFenceAreaCommandInput {

    /**
     * 自定义城市区域变更记录ID
     */
    @NotNull(message = "自定义城市区域变更记录ID不能为空")
    private Long cityAreaChangeWarehouseRecordId;

    /**
     * 围栏区域变更ID
     */
    @NotNull(message = "围栏区域变更ID不能为空")
    private Long fenceAreaChangeRecordsId;

    /**
     * 围栏变更记录ID
     */
    private Long fenceChangeRecordsId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 坐标POI图形
     */
    private String geoShape;

    /**
     * 自定义区域名称
     */
    private String customAreaName;
}
