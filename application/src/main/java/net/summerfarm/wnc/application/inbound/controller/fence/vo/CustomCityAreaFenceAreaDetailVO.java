package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import lombok.Data;

import java.util.List;

/**
 * 自定义围栏详情
 * date: 2025/8/26 17:32<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomCityAreaFenceAreaDetailVO {

    /**
     * 自定义城市区域变更记录ID
     */
    private Long cityAreaChangeWarehouseRecordId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 区域名称
     */
    private String area;

    /**
     * 自定义围栏
     */
    private List<CustomFenceDetailVO> customFenceDetails;

}
