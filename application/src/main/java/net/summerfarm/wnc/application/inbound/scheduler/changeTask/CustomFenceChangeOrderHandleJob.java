package net.summerfarm.wnc.application.inbound.scheduler.changeTask;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.FenceChangTaskForCustomFenceOrderChangeHandleService;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * Description: 围栏切仓订单处理任务
 * date: 2023/8/30 11:22
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CustomFenceChangeOrderHandleJob extends XianMuJavaProcessorV2 {
    @Resource
    private FenceChangTaskForCustomFenceOrderChangeHandleService fenceChangTaskForCustomFenceOrderChangeHandleService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) {
        // 切仓次日凌晨3/4/5点执行
        log.info("自定义围栏切仓订单处理任务开始执行");

        // 查询当前时间点可执行(已过执行时间)且处于待处理的自定义围栏的切仓任务
        List<FenceChangeTaskEnums.Type> types = Collections.singletonList(FenceChangeTaskEnums.Type.CUSTOM);
        List<FenceChangeTaskEntity> waitHandleTasks = fenceChangeTaskRepository.queryExecutableTask(FenceChangeTaskEnums.Status.ORDER_CHANGE_ING,types);

        if (CollectionUtils.isEmpty(waitHandleTasks)){
            return new ProcessResult(true,"普通围栏切仓区域订单处理任务，无可处理切仓订单任务");
        }

        for (FenceChangeTaskEntity waitOrderChangeHandleTask : waitHandleTasks) {
            try {
                fenceChangTaskForCustomFenceOrderChangeHandleService.customFenceChangeOrderHandle(waitOrderChangeHandleTask);
            } catch (Exception e) {
                log.error("自定义围栏切仓订单异常,切仓任务ID:{}", waitOrderChangeHandleTask.getId(), e);
            }
        }
        return new ProcessResult(true);
    }
}
