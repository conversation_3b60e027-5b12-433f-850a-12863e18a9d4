package net.summerfarm.wnc.application.service.changeTask;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskSender;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 切仓任务订单处理服务
 * date: 2025/9/5 10:16<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForNormalFenceOrderChangeHandleService {

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Resource
    private FenceChangeTaskSender fenceChangeTaskSender;

    /**
     * 普通围栏切仓订单处理
     */
    public void normalFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        // 1.根据切仓任务ID查询切仓变更批次
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities = wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(waitOrderChangeHandleTask.getId());

        // 2.变更前后对比，看城配仓是否有变化，运营区域是否有变化，有变化的需要把省市区给找出来构造省市区变更前的城配仓、运营区域、变更后的城配仓、运营区域
        Map<Integer, AreaChangeInfo> areaChangeInfoMap = compareAndBuildAreaChangeInfo(fenceChangeRecordsEntities);
        if (areaChangeInfoMap.isEmpty()) {
            log.info("切仓任务ID:{} 无区域变更，直接完结任务", waitOrderChangeHandleTask.getId());
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
            return;
        }

        // 3.然后根据省市区查询T+1之后待履约的履约单数据
        List<FulfillmentOrderDTO> fulfillmentOrders = queryFulfillmentOrders(areaChangeInfoMap, waitOrderChangeHandleTask);

        // 4.过滤出城配仓有变更的订单数据
        List<FulfillmentOrderDTO> needChangeOrders = filterNeedChangeOrders(fulfillmentOrders, areaChangeInfoMap);

        // 5.保存到切仓订单明细表
        List<FenceChangeTaskOrderEntity> waitHandleTaskOrders = saveTaskOrderDetails(needChangeOrders, waitOrderChangeHandleTask.getId());

        // 6.调用OFC进行切仓
        List<String> failOrders = new ArrayList<>();
        for (FenceChangeTaskOrderEntity waitHandleTaskOrder : waitHandleTaskOrders) {
            try {
                fenceChangeTaskDomainService.doFenceChangeOrderHandleNew(waitHandleTaskOrder);
            }catch (Throwable e){
                failOrders.add(waitHandleTaskOrder.getOuterOrderId());
                log.info("围栏切仓订单处理任务-履约单处理失败，等待失败订单重试，异常原因：{}", e.getMessage(), e);
                FenceChangeTaskOrderEntity updateOrderFail = waitHandleTaskOrder.execute(FenceChangeTaskDetailEnums.Status.FAIL, e.getMessage());
                fenceChangeTaskDetailRepository.update(updateOrderFail);
            }
        }
        // 更新切仓任务状态
        fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));

        if (!failOrders.isEmpty()){
            //存在订单切仓失败 发送飞书消息通知
            fenceChangeTaskSender.sendOrderFailMsg(waitOrderChangeHandleTask, failOrders.size());
        }
    }

    /**
     * 比较变更前后的数据，构建区域变更信息
     * 以adCodeMsgId为维度处理变更
     */
    private Map<Integer, AreaChangeInfo> compareAndBuildAreaChangeInfo(List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities) {
        Map<Integer, AreaChangeInfo> areaChangeInfoMap = new HashMap<>();

        // 获取所有区域变更记录，按adCodeMsgId分组处理
        Map<Integer, WncFenceAreaChangeRecordsEntity> adCodeAreaRecordsMap = new HashMap<>();
        Map<Integer, WncFenceChangeRecordsEntity> beforeRecordsMap = new HashMap<>();
        Map<Integer, WncFenceChangeRecordsEntity> afterRecordsMap = new HashMap<>();

        // 收集所有变更前后的记录
        for (WncFenceChangeRecordsEntity record : fenceChangeRecordsEntities) {
            if (Objects.equals(record.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue())) { // 变更前
                beforeRecordsMap.put(record.getFenceId(), record);
                // 收集变更前的区域记录
                if (!CollectionUtils.isEmpty(record.getAreaChangeRecords())) {
                    for (WncFenceAreaChangeRecordsEntity areaRecord : record.getAreaChangeRecords()) {
                        if (areaRecord.getAdCodeMsgId() != null) {
                            adCodeAreaRecordsMap.put(areaRecord.getAdCodeMsgId(), areaRecord);
                        }
                    }
                }
            } else if (Objects.equals(record.getFenceChangeStage(), WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue())) { // 变更后
                afterRecordsMap.put(record.getFenceId(), record);
            }
        }

        // 按adCodeMsgId维度分析变更
        for (Map.Entry<Integer, WncFenceAreaChangeRecordsEntity> entry : adCodeAreaRecordsMap.entrySet()) {
            Integer adCodeMsgId = entry.getKey();

            // 获取该区域对应的围栏变更前后记录
            WncFenceAreaChangeRecordsEntity areaRecord = entry.getValue();
            Integer fenceId = areaRecord.getFenceId();

            WncFenceChangeRecordsEntity beforeRecord = beforeRecordsMap.get(fenceId);
            WncFenceChangeRecordsEntity afterRecord = afterRecordsMap.get(fenceId);

            if (beforeRecord == null || afterRecord == null) {
                log.warn("未找到围栏{}的完整变更记录，跳过adCodeMsgId:{}", fenceId, adCodeMsgId);
                continue;
            }

            // 检查城配仓或运营区域是否有变化
            boolean storeChanged = !Objects.equals(beforeRecord.getFenceStoreNo(), afterRecord.getFenceStoreNo());
            boolean areaNoChanged = !Objects.equals(beforeRecord.getFenceAreaNo(), afterRecord.getFenceAreaNo());
            boolean fenceNoChanged = !Objects.equals(beforeRecord.getFenceId(), afterRecord.getFenceId());

            if (storeChanged || areaNoChanged || fenceNoChanged) {
                String city = areaRecord.getCity();
                String area = areaRecord.getArea();

                if (city == null || city.trim().isEmpty()) {
                    log.warn("区域变更记录城市为空，跳过处理，adCodeMsgId:{}", adCodeMsgId);
                    continue;
                }

                AreaChangeInfo changeInfo = new AreaChangeInfo();
                changeInfo.setAdCodeMsgId(adCodeMsgId);
                changeInfo.setCity(city);
                changeInfo.setArea(area);
                changeInfo.setOldFenceId(beforeRecord.getFenceId());
                changeInfo.setOldStoreNo(beforeRecord.getFenceStoreNo());
                changeInfo.setOldAreaNo(beforeRecord.getFenceAreaNo());

                changeInfo.setNewFenceId(afterRecord.getFenceId());
                changeInfo.setNewStoreNo(afterRecord.getFenceStoreNo());
                changeInfo.setNewAreaNo(afterRecord.getFenceAreaNo());

                changeInfo.setStoreChanged(storeChanged);
                changeInfo.setAreaChanged(areaNoChanged);
                changeInfo.setFenceChanged(fenceNoChanged);

                areaChangeInfoMap.put(adCodeMsgId, changeInfo);
                log.debug("构建区域变更信息，adCodeMsgId:{}, 城市:{}, 区域:{}, 城配仓变更:{}->{}, 运营区域变更:{}->{}",
                        adCodeMsgId, city, area,
                        beforeRecord.getFenceStoreNo(), afterRecord.getFenceStoreNo(),
                        beforeRecord.getFenceAreaNo(), afterRecord.getFenceAreaNo());
            }
        }

        return areaChangeInfoMap;
    }

    /**
     * 查询T+1之后待履约的履约单数据
     */
    private List<FulfillmentOrderDTO> queryFulfillmentOrders(Map<Integer, AreaChangeInfo> areaChangeInfoMap,
                                                           FenceChangeTaskEntity waitOrderChangeHandleTask) {
        List<FulfillmentOrderDTO> allFulfillmentOrders = new ArrayList<>();

        // 本身就是同一个城市按照城配仓分组即可
        Map<Integer, List<AreaChangeInfo>> oldStoreNoChangeMap = areaChangeInfoMap.values().stream()
                .collect(Collectors.groupingBy(
                        AreaChangeInfo::getOldStoreNo
                ));

        oldStoreNoChangeMap.forEach((oldStoreNo, changeInfos) -> {
            AreaChangeInfo areaChangeInfo = changeInfos.get(0);

            String city = areaChangeInfo.getCity();
            List<String> areas = changeInfos.stream().map(AreaChangeInfo::getArea).distinct().collect(Collectors.toList());

            FulfillmentQueryInput queryInput = FulfillmentQueryInput.builder()
                    .city(city)
                    .areas(areas)
                    .storeNo(oldStoreNo)
                    .deliveryDateBegin(waitOrderChangeHandleTask.getExeTimePlus2Date()) // T+2的时间点
                    .build();

            try {
                List<FulfillmentOrderDTO> fulfillmentOrders = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(queryInput);
                if (!CollectionUtils.isEmpty(fulfillmentOrders)) {
                    // 为每个订单设置对应的adCodeMsgId信息
                    for (FulfillmentOrderDTO order : fulfillmentOrders) {
                        // 根据订单的城市区域找到对应的adCodeMsgId
                        Integer matchedAdCodeMsgId = findMatchingAdCodeMsgId(order, areaChangeInfoMap);
                        if (matchedAdCodeMsgId != null) {
                            order.setOldAdCodeMsgId(matchedAdCodeMsgId);
                            order.setNewAdCodeMsgId(matchedAdCodeMsgId);
                        }
                    }
                    allFulfillmentOrders.addAll(fulfillmentOrders);
                }
            } catch (Exception e) {
                log.error("查询履约单数据异常，城市:{}, 区域:{}, 城配仓:{}, 异常信息:{}",
                        city, JSON.toJSONString(areas), oldStoreNo, e.getMessage(), e);
            }
        });

        log.info("切仓任务ID:{} 查询到待履约订单数量:{}", waitOrderChangeHandleTask.getId(), allFulfillmentOrders.size());
        return allFulfillmentOrders;
    }

    /**
     * 根据订单的城市区域找到匹配的adCodeMsgId
     */
    private Integer findMatchingAdCodeMsgId(FulfillmentOrderDTO order, Map<Integer, AreaChangeInfo> areaChangeInfoMap) {
        String orderCity = order.getCity();
        String orderArea = order.getArea();

        if (orderCity == null || orderArea == null) {
            return null;
        }

        return areaChangeInfoMap.values().stream()
                .filter(info -> orderCity.equals(info.getCity()) && orderArea.equals(info.getArea()))
                .map(AreaChangeInfo::getAdCodeMsgId)
                .findFirst()
                .orElse(null);
    }

    /**
     * 过滤出城配仓有变更的订单数据
     */
    private List<FulfillmentOrderDTO> filterNeedChangeOrders(List<FulfillmentOrderDTO> fulfillmentOrders,
                                                           Map<Integer, AreaChangeInfo> areaChangeInfoMap) {
        if (CollectionUtils.isEmpty(fulfillmentOrders)) {
            return Collections.emptyList();
        }

        List<FulfillmentOrderDTO> needChangeOrders = new ArrayList<>();

        for (FulfillmentOrderDTO order : fulfillmentOrders) {
            // 检查订单的城市是否为空
            String orderCity = order.getCity();

            if (orderCity == null || orderCity.trim().isEmpty()) {
                log.warn("履约订单城市为空，跳过处理，订单号:{}", order.getOuterOrderId());
                continue;
            }

            // 根据订单的adCodeMsgId查找变更信息
            Integer adCodeMsgId = order.getOldAdCodeMsgId();
            if (adCodeMsgId == null) {
                // 如果订单没有adCodeMsgId，尝试通过城市区域匹配
                adCodeMsgId = findMatchingAdCodeMsgId(order, areaChangeInfoMap);
            }

            if (adCodeMsgId != null) {
                AreaChangeInfo changeInfo = areaChangeInfoMap.get(adCodeMsgId);
                if (changeInfo != null && changeInfo.isStoreChanged()) {
                    // 设置变更信息到订单中
                    order.setOldStoreNo(changeInfo.getOldStoreNo());
                    order.setNewStoreNo(changeInfo.getNewStoreNo());
                    order.setOldAreaNo(changeInfo.getOldAreaNo());
                    order.setNewAreaNo(changeInfo.getNewAreaNo());
                    order.setOldFenceId(changeInfo.getOldFenceId());
                    order.setNewFenceId(changeInfo.getNewFenceId());

                    needChangeOrders.add(order);
                }
            }
        }

        log.info("过滤出需要切仓的订单数量:{}", needChangeOrders.size());
        return needChangeOrders;
    }

    /**
     * 保存到切仓订单明细表
     */
    private List<FenceChangeTaskOrderEntity> saveTaskOrderDetails(List<FulfillmentOrderDTO> needChangeOrders, Long taskId) {
        if (CollectionUtils.isEmpty(needChangeOrders)) {
            return Collections.emptyList();
        }

        List<FenceChangeTaskOrderEntity> taskOrderEntities = new ArrayList<>();

        for (FulfillmentOrderDTO order : needChangeOrders) {
            try {
                FenceChangeTaskOrderEntity taskOrderEntity = FenceChangeTaskOrderConverter.dto2Entity(order);
                taskOrderEntity.create(taskId);
                taskOrderEntities.add(taskOrderEntity);
            } catch (Exception e) {
                log.error("转换订单数据异常，订单号:{}, 异常信息:{}", order.getOuterOrderId(), e.getMessage(), e);
            }
        }

        if (!CollectionUtils.isEmpty(taskOrderEntities)) {
            try {
                // 去重处理，避免重复插入
                fenceChangeTaskDomainService.saveBatchDetail(taskOrderEntities);
                log.info("保存切仓订单明细成功，任务ID:{}, 订单数量:{}", taskId, taskOrderEntities.size());
                return taskOrderEntities;
            } catch (DuplicateKeyException e) {
                log.warn("保存切仓订单明细存在重复数据，任务ID:{}, 异常信息:{}", taskId, e.getMessage());
                return taskOrderEntities;
            } catch (Exception e) {
                log.error("保存切仓订单明细异常，任务ID:{}, 异常信息:{}", taskId, e.getMessage(), e);
                throw e;
            }
        }

        return taskOrderEntities;
    }

    /**
     * 区域变更信息
     */
    @Data
    private static class AreaChangeInfo {
        private Integer adCodeMsgId;
        private Integer oldFenceId;
        private Integer newFenceId;
        private String city;
        private String area;
        private Integer oldStoreNo;
        private Integer newStoreNo;
        private Integer oldAreaNo;
        private Integer newAreaNo;
        private boolean storeChanged;
        private boolean areaChanged;
        private boolean fenceChanged;
    }

}
