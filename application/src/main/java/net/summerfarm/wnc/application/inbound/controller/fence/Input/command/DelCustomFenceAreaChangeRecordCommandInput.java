package net.summerfarm.wnc.application.inbound.controller.fence.Input.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 删除自定义围栏变更记录
 * date: 2025/8/27 10:31<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DelCustomFenceAreaChangeRecordCommandInput {


    /**
     * 自定义城市区域变更记录ID
     */
    @NotNull(message = "自定义城市区域变更记录ID不能为空")
    private Long cityAreaChangeWarehouseRecordId;

    /**
     * 围栏区域变更记录ID
     */
    @NotNull(message = "围栏区域变更记录ID不能为空")
    private Long fenceAreaChangeRecordsId;
}
