package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import lombok.Data;

import java.util.List;

/**
 * 自定义围栏详情VO
 * date: 2025/8/29<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomFenceDetailVO {

    /**
     * 围栏变更记录ID
     */
    private Long fenceChangeRecordsId;

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 围栏状态 0正常  1失效  2删除  3暂停
     */
    private Integer fenceStatus;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 自定义区域详情
     */
    private List<CustomAreaDetail> customAreaDetails;
}
