package net.summerfarm.wnc.application.inbound.controller.fence.Input.query;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 自定义区域详情查询
 * date: 2025/8/26 18:29<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomCityAreaFenceAreaDetailQueryInput {

    /**
     * 自定义城市区域变更记录ID
     */
    @NotNull(message = "自定义城市区域变更记录ID不能为空")
    private Long cityAreaChangeWarehouseRecordId;
}
