package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import lombok.Data;

/**
 * 自定义区域详情VO
 * date: 2025/8/29<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomAreaDetail {
    
    /**
     * 围栏区域变更ID
     */
    private Long fenceAreaChangeRecordsId;

    /**
     * 地区ID
     */
    private Integer adCodeMsgId;

    /**
     * 自定义区域名称
     */
    private String customAreaName;

    /**
     * 区域状态 0 正常 1 失效 2删除 3停用
     */
    private Integer adCodeMsgStatus;

    /**
     * geo-shape 图形集合
     */
    private String geoShape;
}
