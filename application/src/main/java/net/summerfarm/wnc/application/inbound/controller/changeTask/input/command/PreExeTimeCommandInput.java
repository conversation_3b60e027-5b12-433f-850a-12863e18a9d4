package net.summerfarm.wnc.application.inbound.controller.changeTask.input.command;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 预约切仓时间
 * date: 2025/8/27 16:48<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PreExeTimeCommandInput {

    /**
     * 预约切仓时间
     */
    @NotNull(message = "预约切仓时间不能为空")
    private LocalDateTime preExeTime;

    /**
     * 自定义城市区域变更记录ID
     */
    @NotNull(message = "自定义城市区域变更记录ID不能为空")
    private Long cityAreaChangeWarehouseRecordId;
}
