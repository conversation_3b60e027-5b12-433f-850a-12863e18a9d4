package net.summerfarm.wnc.application.inbound.provider.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.wnc.application.inbound.provider.fence.converter.AdCodeMsgDubboConverter;
import net.summerfarm.wnc.client.provider.fence.AdCodeMsgQueryProvider;
import net.summerfarm.wnc.client.req.fence.AdCodePageQueryReq;
import net.summerfarm.wnc.client.req.fence.DeliveryCityAreasQueryReq;
import net.summerfarm.wnc.client.req.fence.StoreNoAddrStatusQueryReq;
import net.summerfarm.wnc.client.resp.fence.AdCodeMsgResp;
import net.summerfarm.wnc.client.resp.fence.DeliveryCityAreasResp;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgByStoreNoAddrStatusQuery;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.dataObject.AdCodeMsgFenceStoreDO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2024/11/15 10:29<br/>
 *
 * <AUTHOR> />
 */
@DubboService
public class AdCodeMsgQueryProviderImpl implements AdCodeMsgQueryProvider {

    @Resource
    private AdCodeMsgRepository adCodeMsgRepository;

    @Override
    public DubboResponse<PageInfo<AdCodeMsgResp>> queryAdCodeMsgPage(AdCodePageQueryReq req) {
        if(req.getPageIndex() == null || req.getPageSize() == null){
            throw new TmsRuntimeException("分页参数不能为空");
        }
        AdCodeMsgQuery adCodeMsgQuery = AdCodeMsgQuery.builder()
                .adCode(req.getAdCode())
                .province(req.getProvince())
                .city(req.getCity())
                .area(req.getArea())
                .status(req.getStatus())
                .fenceId(req.getFenceId())
                .adCodeList(req.getAdCodeList())
                .fenceStatus(req.getFenceStatus())
                .storeNos(req.getStoreNos())
                .storeStatus(req.getStoreStatus())
                .build();
        adCodeMsgQuery.setPageIndex(req.getPageIndex());
        adCodeMsgQuery.setPageSize(req.getPageSize());

        PageInfo<AdCodeMsgFenceStoreDO> adCodeMsgFenceStoreDOPageInfo = adCodeMsgRepository.queryPage(adCodeMsgQuery);
        PageInfo<AdCodeMsgResp> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(adCodeMsgFenceStoreDOPageInfo, resultPageInfo);
        if(!CollectionUtils.isEmpty(adCodeMsgFenceStoreDOPageInfo.getList())){
            resultPageInfo.setList(adCodeMsgFenceStoreDOPageInfo.getList().stream().map(AdCodeMsgDubboConverter::do2Resp).collect(Collectors.toList()));
        }
        return DubboResponse.getOK(resultPageInfo);
    }

    @Override
    public DubboResponse<List<AdCodeMsgResp>> queryAdCodeMsgByStoreNoAddrStatus(@Valid StoreNoAddrStatusQueryReq req) {
        List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgRepository.queryAdCodeMsgByStoreNoAddrStatus(AdCodeMsgByStoreNoAddrStatusQuery.builder()
                .storeNo(req.getStoreNo())
                .adCode(req.getAdCode())
                .adCodeMsgStatus(req.getAdCodeMsgStatus())
                .fenceStatus(req.getFenceStatus())
                .warehouseLogisticsCenterStatus(req.getWarehouseLogisticsCenterStatus())
                .provinceCityAreaLikeName(req.getProvinceCityAreaLikeName())
                .cityLikeName(req.getCityLikeName())
                .build());

        return DubboResponse.getOK(adCodeMsgEntities.stream().map(AdCodeMsgDubboConverter::entity2Resp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<DeliveryCityAreasResp> queryDeliveryCityAreas(@Valid DeliveryCityAreasQueryReq deliveryCityAreasQueryReq) {
        return null;
    }
}
