package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 自定义城市区域
 * date: 2025/8/27 17:38<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomCityAreaPageVO {
    /**
     * 自定义城市区域变更记录ID
     */
    private Long cityAreaChangeWarehouseRecordId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String area;

    /**
     * 区域
     */
    private String city;

    /**
     * 变更状态 0 等待生效 10正常生效中 20已结束
     */
    private Integer changeStatus;

    /**
     * 预约执行切仓时间
     */
    private LocalDateTime preExeTime;

    /**
     * 围栏变更批次号
     */
    private String changeBatchNo;

    /**
     * 切仓任务ID
     */
    private Long fenceChangeTaskId;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 完结结束时间
     */
    private LocalDateTime overTime;

    /**
     * 区域定义类型10 普通范围区域  20自定义范围区域
     */
    private Integer areaDefinationType;

}
