package net.summerfarm.wnc.application.inbound.controller.changeTask;

import net.summerfarm.wnc.application.inbound.controller.changeTask.input.command.PreExeTimeCommandInput;
import net.summerfarm.wnc.application.inbound.controller.changeTask.input.query.FenceChangeTaskDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.changeTask.vo.CustomFenceChangeTaskDetailVO;
import net.summerfarm.wnc.application.inbound.controller.changeTask.converter.FenceChangeTaskVOConverter;
import net.summerfarm.wnc.application.service.changeTask.FenceChangeTaskCommandService;
import net.summerfarm.wnc.application.service.changeTask.FenceChangeTaskQueryService;
import net.summerfarm.wnc.application.service.changeTask.dto.CustomFenceChangeTaskDetailDTO;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 新切仓任务
 * date: 2025/8/27 16:31<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/fence-change-task-new")
public class FenceChangeTaskNewController {

    @Resource
    private FenceChangeTaskCommandService fenceChangeTaskCommandService;
    @Resource
    private FenceChangeTaskQueryService fenceChangeTaskQueryService;

    /**
     * 更新自定义围栏区域预约切仓时间
     * @param input 删除
     * @return 结果
     */
    @PostMapping("/upsert/pre-exe-time")
    @RequiresPermissions(value = {"fence-change-task-new:pre-exe-time"})
    public CommonResult<Void> preExeTime(@RequestBody @Validated PreExeTimeCommandInput input) {
        fenceChangeTaskCommandService.preExeTime(input.getPreExeTime(),input.getCityAreaChangeWarehouseRecordId());
        return CommonResult.ok();
    }


    /**
     * 查询自定义围栏切仓任务详情
     * @param input 查询
     * @return 结果
     */
    @PostMapping("/query/custom-fence-change-task-detail")
    public CommonResult<CustomFenceChangeTaskDetailVO> queryCustomFenceChangeTaskDetail(@RequestBody @Validated FenceChangeTaskDetailQueryInput input) {
        CustomFenceChangeTaskDetailDTO dto = fenceChangeTaskQueryService.queryCustomFenceChangeTaskDetail(input.getChangeTaskId());
        CustomFenceChangeTaskDetailVO result = FenceChangeTaskVOConverter.toCustomFenceChangeTaskDetailVO(dto);
        return CommonResult.ok(result);
    }


}
