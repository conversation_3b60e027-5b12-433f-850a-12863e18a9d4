package net.summerfarm.wnc.domain.fence.service;


import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 围栏区域变更记录领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Service
public class WncFenceAreaChangeRecordsQueryDomainService {


}
