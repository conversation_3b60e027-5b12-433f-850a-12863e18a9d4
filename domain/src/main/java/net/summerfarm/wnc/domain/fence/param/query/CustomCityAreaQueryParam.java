package net.summerfarm.wnc.domain.fence.param.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

/**
 * 自定义围栏区域查询
 * date: 2025/8/28 10:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomCityAreaQueryParam extends BasePageInput {

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 运营服务区编号
     */
    private Integer areaNo;

    /**
     * 自定义城市区域变更状态 0 等待生效 10正常生效中 20已结束
     */
    private Integer cityAreaChangeStatus;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;
}
