package net.summerfarm.wnc.domain.fence.repository;



import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;

import java.util.ArrayList;
import java.util.List;


/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncCityAreaChangeWarehouseRecordsCommandRepository {

    WncCityAreaChangeWarehouseRecordsEntity insertSelective(WncCityAreaChangeWarehouseRecordsCommandParam param);

    int updateSelectiveById(WncCityAreaChangeWarehouseRecordsCommandParam param);

    int remove(Long id);

    /**
     * 批量更新
     * @param params 参数列表
     */
    void batchUpdate(ArrayList<WncCityAreaChangeWarehouseRecordsCommandParam> params);

    /**
     * 批量插入
     * @param params 参数列表
     * @return 插入的记录数
     */
    int batchInsert(List<WncCityAreaChangeWarehouseRecordsCommandParam> params);

    /**
     * 根据切仓任务ID更新城市区域切仓记录的状态
     * @param fenceChangeTaskId 切仓任务ID
     * @param changeStatus 状态值
     */
    void updateChangeStatusByFenceChangeTaskId(Long fenceChangeTaskId, Integer changeStatus);

    /**
     * 重置自定义区域的切仓任务
     * @param fenceChangeTaskId 切仓任务ID
     */
    void resetCustomAreaFenceChangeTask(Long fenceChangeTaskId);

    /**
     * 批量更新城市区域切仓记录状态
     * @param cityAreaChangeIds 城市区域切仓记录ID集合
     * @param sourceStatus 旧状态
     * @param targetStatus 新状态
     */
    void updateChangeStatusByIds(List<Long> cityAreaChangeIds, Integer sourceStatus, Integer targetStatus);
}