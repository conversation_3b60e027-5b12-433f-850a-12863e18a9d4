package net.summerfarm.wnc.domain.fence.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam;



/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncFenceAreaChangeRecordsQueryRepository {

    PageInfo<WncFenceAreaChangeRecordsEntity> getPage(WncFenceAreaChangeRecordsQueryParam param);

    WncFenceAreaChangeRecordsEntity selectById(Long id);

    List<WncFenceAreaChangeRecordsEntity> selectByCondition(WncFenceAreaChangeRecordsQueryParam param);

    /**
     * 根据围栏变更记录ID集合查询围栏区域变更记录
     * @param fenceChangeIds 围栏变更记录ID集合
     * @return 围栏区域变更记录集合
     */
    List<WncFenceAreaChangeRecordsEntity> selectByFenceChangeIds(List<Long> fenceChangeIds);
}