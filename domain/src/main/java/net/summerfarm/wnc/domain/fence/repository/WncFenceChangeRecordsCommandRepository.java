package net.summerfarm.wnc.domain.fence.repository;



import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.ArrayList;
import java.util.List;


/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncFenceChangeRecordsCommandRepository {

    WncFenceChangeRecordsEntity insertSelective(WncFenceChangeRecordsCommandParam param);

    int updateSelectiveById(WncFenceChangeRecordsCommandParam param);

    int remove(Long id);

    /**
     * 根据变更批次号更新预约切仓时间及切仓任务ID
     * @param preExeTime 预约切仓时间
     * @param fenceChangeTaskId 切仓任务ID
     * @param changeBatchNo 变更批次号
     */
    void updatePreExeTimeAndFenceChangeTaskIdByChangeBatchNo(LocalDateTime preExeTime, Long fenceChangeTaskId, String changeBatchNo);

    /**
     * 取消切仓任务
     * @param fenceChangeTaskId 切仓任务ID
     */
    void cancelFenceChangeTask(Long fenceChangeTaskId);

    /**
     * 批量插入
     * @param params 参数列表
     * @return 插入的记录数
     */
    int batchInsert(List<WncFenceChangeRecordsCommandParam> params);
}