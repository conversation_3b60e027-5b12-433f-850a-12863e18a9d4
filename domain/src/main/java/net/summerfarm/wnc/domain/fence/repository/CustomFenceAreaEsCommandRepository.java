package net.summerfarm.wnc.domain.fence.repository;

import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;

import java.util.List;

/**
 * Description: <br/>
 * date: 2025/9/3 11:07<br/>
 *
 * <AUTHOR> />
 */
public interface CustomFenceAreaEsCommandRepository {
    /**
     * 保存自定义围栏区域
     * @param customFenceAreaEsEntities 自定义围栏区域实体集合
     */
    void saveAll(List<CustomFenceAreaEsEntity> customFenceAreaEsEntities);
}
