package net.summerfarm.wnc.domain.fence.param.command;

import lombok.Data;

import java.util.List;

/**
 * 更新自定义围栏变更记录参数
 * date: 2025/9/3 <br/>
 *
 * <AUTHOR> />
 */
@Data
public class UpdateCustomFenceChangeRecordCommandParam {

    /**
     * 围栏变更记录ID
     */
    private Long fenceChangeRecordsId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 运营区域编号
     */
    private Integer areaNo;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 围栏类型 0：新建围栏，1：围栏拆分 2：自定义围栏
     */
    private Integer type;

    /**
     * 围栏配送周期
     */
    private FenceDeliveryCommandParam fenceDeliveryCommandParam;

    /**
     * 下单渠道类型 逗号分隔  1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
     */
    private String orderChannelType;

    /**
     * 围栏渠道业务白名单
     */
    private List<FenceChannelBusinessWhiteConfigCommandParam> fenceChannelBusinessWhiteConfigCommandParams;
}
