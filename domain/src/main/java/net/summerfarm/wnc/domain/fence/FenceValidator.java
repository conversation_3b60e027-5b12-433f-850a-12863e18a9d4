package net.summerfarm.wnc.domain.fence;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.FenceDeliveryEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceDeliveryEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:围栏校验器
 * date: 2023/11/29 16:37
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class FenceValidator {

    private final FenceRepository fenceRepository;
    private final AdCodeMsgRepository adCodeMsgRepository;

    public void validateFence(FenceEntity fenceEntity) {
        this.validateFenceName(fenceEntity.getFenceName());
        this.validateNewFenceArea(fenceEntity.getAdCodeMsgEntities(), fenceEntity.getId());
        this.validateFenceDelivery(fenceEntity.getFenceDeliveryEntity(), null);
        this.validateFenceChannleType(fenceEntity.getOrderChannelType());
        this.validateFenceChannelBusinessWhiteConfig(fenceEntity.getOrderChannelType(), fenceEntity.getFenceChannelBusinessWhiteConfigEntities());
    }

    public void validateCustomFence(FenceEntity fenceEntity) {
        this.validateFenceName(fenceEntity.getFenceName());
        this.validateFenceDelivery(fenceEntity.getFenceDeliveryEntity(), null);
        this.validateFenceChannleType(fenceEntity.getOrderChannelType());
        this.validateFenceChannelBusinessWhiteConfig(fenceEntity.getOrderChannelType(), fenceEntity.getFenceChannelBusinessWhiteConfigEntities());
    }

    public void validateFenceChannelBusinessWhiteConfig(String orderChannelType, List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntities) {
        if(StrUtil.isBlank(orderChannelType)){
            throw new BizException("渠道类型不能为空");
        }
        if(CollectionUtils.isEmpty(fenceChannelBusinessWhiteConfigEntities)){
            throw new BizException("渠道业务详情不能为空");
        }
        List<String> channelTypes = Arrays.stream(orderChannelType.split(",")).distinct().collect(Collectors.toList());
        // 校验渠道业务是否一致
        List<String> channelTypeBusinessTypes = fenceChannelBusinessWhiteConfigEntities.stream().map(FenceChannelBusinessWhiteConfigEntity::getOrderChannelType).distinct().collect(Collectors.toList());
        if(channelTypes.size() != channelTypeBusinessTypes.size()){
            throw new BizException("渠道业务详情与渠道类型数量不对应");
        }
        channelTypeBusinessTypes.forEach(channelTypeBusiness -> {
            if(!channelTypes.contains(channelTypeBusiness)){
                throw new BizException("渠道业务详情与渠道类型不对应");
            }
        });
    }

    public void validateFenceChannleType(String orderChannelType) {
        if(StrUtil.isBlank(orderChannelType)){
            throw new BizException("渠道类型不能为空");
        }
        List<String> types = Arrays.asList(orderChannelType.split(","));
        types.forEach(FenceEnums.OrderChannelType::getChannelByValue);
    }

    public void validateFenceName(String fenceName) {
        if (StrUtil.isBlank(fenceName)){
            return;
        }
        if(StrUtil.isBlank(fenceName.trim())){
            throw new BizException("围栏名称不能为空");
        }
        List<FenceEntity> fenceEntities = fenceRepository.queryList(FenceQuery.builder().fenceName(fenceName).build());
        if(!CollectionUtils.isEmpty(fenceEntities)){
            throw new BizException("围栏名称不能重复");
        }
    }

    public void validateFenceDelivery(FenceDeliveryEntity fenceDeliveryEntity, LocalDate oldNextDeliveryDate) {
        if (fenceDeliveryEntity == null){
            return;
        }
        LocalDate newNextDeliveryDate = fenceDeliveryEntity.getNextDeliveryDate();
        boolean isChange = !Objects.equals(newNextDeliveryDate, oldNextDeliveryDate);
        //判断首配是否发生变化 发生变化进行首配的再校验
        if (isChange && !newNextDeliveryDate.isAfter(LocalDate.now())) {
            throw new BizException("首次配送时间必须大于当前日期");
        }

        if (Objects.equals(fenceDeliveryEntity.getFrequentMethod(), FenceDeliveryEnums.FrequentMethod.WEEK_CALC.getValue())) {
            if (StringUtils.isNotBlank(fenceDeliveryEntity.getDeliveryFrequent())) {
                int dayOfWeek = newNextDeliveryDate.getDayOfWeek().getValue();

                String[] deliveryFrequent = fenceDeliveryEntity.getDeliveryFrequent().split(AppConsts.Symbol.COMMA);
                //不是每天配送 && 配送日和配送周期不匹配
                if (Arrays.stream(deliveryFrequent).noneMatch(el -> {
                    if (Objects.equals(el, "0")) {
                        return true;
                    } else {
                        return Objects.equals(el, Integer.toString(dayOfWeek));
                    }
                })) {
                    throw new BizException("首次配送时间和配送周期不匹配，请将首次配送时间设置为可配送日");
                }
            }
        }
        if(Objects.equals(fenceDeliveryEntity.getFrequentMethod(), FenceDeliveryEnums.FrequentMethod.INTERVAL_CALC.getValue())){
            LocalDate beginCalculateDate = fenceDeliveryEntity.getBeginCalculateDate();
            Integer deliveryFrequentInterval = fenceDeliveryEntity.getDeliveryFrequentInterval();
            if(beginCalculateDate == null){
                throw new BizException("开始计算日期不能为空");
            }
            if(deliveryFrequentInterval == null){
                throw new BizException("配送间隔周期不能为空");
            }
            if(beginCalculateDate.isAfter(LocalDate.now())){
                throw new BizException("开始计算日期只能当天及当天之前");
            }
            //判断首配日期是否在开始计算日期之前
            while(newNextDeliveryDate.compareTo(beginCalculateDate) > 0){
                beginCalculateDate = beginCalculateDate.plusDays(deliveryFrequentInterval);
                if(beginCalculateDate.compareTo(newNextDeliveryDate) == 0){
                    return;
                }
            }
            throw new BizException("首次配送时间和配送周期不匹配，请将首次配送时间设置为可配送日");
        }
    }

    public void validateNewFenceArea(List<AdCodeMsgEntity> fenceAreas, Integer fenceId) {
        if(CollectionUtils.isEmpty(fenceAreas)){
            return;
        }
        AdCodeMsgEntity firstArea = fenceAreas.get(0);
        List<AdCodeMsgEntity> areasWithCity = adCodeMsgRepository.queryByCityAndNotFenceId(firstArea.getCity(), fenceId);
        //优先判断所选区域是否是行政市级别围栏(行政市级别围栏下无行政区域)
        if(Objects.equals(firstArea.getLevel(),"city")){
            if (!CollectionUtils.isEmpty(areasWithCity)){
                throw new BizException(String.format("%s 已经存在该行政市级别围栏区域", firstArea.getCity()));
            }
        }else {
            List<AdCodeMsgEntity> cityAreas = areasWithCity.stream().filter(e -> Objects.equals(e.getLevel(), "city")).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(cityAreas)){
                throw new BizException(String.format("%s 已经存在该行政市级别围栏区域", firstArea.getCity()));
            }
            //校验区是否重复
            Map<String, List<AdCodeMsgEntity>> existedAreaMap = areasWithCity.stream().collect(Collectors.groupingBy(AdCodeMsgEntity::getArea));
            for (AdCodeMsgEntity fenceArea : fenceAreas) {
                if (!CollectionUtils.isEmpty(existedAreaMap.get(fenceArea.getArea()))) {
                    String stringBuffer = fenceArea.getProvince() + fenceArea.getCity() + fenceArea.getArea() + "已经存在该围栏区域";
                    throw new BizException(stringBuffer);
                }
            }
        }
    }

    public void validateFenceEditArea(FenceEntity editedFenceEntity, FenceEntity existedFenceEntity) {
        if (editedFenceEntity == null || existedFenceEntity == null){
            return;
        }
        List<AdCodeMsgEntity> editedFenceAreas = Optional.ofNullable(editedFenceEntity.getAdCodeMsgEntities()).orElse(Lists.newArrayList());
        List<AdCodeMsgEntity> existedFenceAreas = Optional.ofNullable(existedFenceEntity.getAdCodeMsgEntities()).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(editedFenceAreas) && CollectionUtils.isEmpty(existedFenceAreas)){
            return;
        }
        //围栏编辑不支持直接删除区域 前端所传区域列表editedFenceAreas一定包含围栏现有区域existedFenceAreas
        boolean allMatch = existedFenceAreas.stream().allMatch(e -> editedFenceAreas.stream().map(AdCodeMsgEntity::buildUk).collect(Collectors.toSet()).contains(e.buildUk()));
        if (!allMatch){
            throw new BizException("围栏区域不能删除");
        }
        //需要筛选出本次开放的新区域进行区域重复性校验 遍历前端所传区域列表editedFenceAreas 找出不在围栏现有区域existedFenceAreas的区域
        List<AdCodeMsgEntity> newFenceAreas = editedFenceAreas.stream().filter(e ->!existedFenceAreas.stream().map(AdCodeMsgEntity::buildUk).collect(Collectors.toSet()).contains(e.buildUk())).collect(Collectors.toList());
        //重置围栏区域为新增区域
        editedFenceEntity.setAdCodeMsgEntities(newFenceAreas);
        //本次无新增区域
        if (CollectionUtils.isEmpty(newFenceAreas)){
            return;
        }
        this.validateNewFenceArea(newFenceAreas, existedFenceEntity.getId());
    }
}
