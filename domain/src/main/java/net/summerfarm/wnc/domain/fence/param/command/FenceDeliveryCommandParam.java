package net.summerfarm.wnc.domain.fence.param.command;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * Description:围栏配送周期更新命令
 * date: 2023/10/27 15:12
 *
 * <AUTHOR>
 */
@Data
public class FenceDeliveryCommandParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 首配日
     */
    private LocalDate nextDeliveryDate;

    /**
     * 周期方案，1：周计算，2：间隔计算
     */
    private Integer frequentMethod;

    /**
     * 配送周期
     */
    private String deliveryFrequent;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

}
