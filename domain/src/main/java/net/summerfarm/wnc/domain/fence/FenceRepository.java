package net.summerfarm.wnc.domain.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.query.fence.FencePageQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.fence.dataObject.AddrDO;
import net.summerfarm.wnc.domain.fence.dataObject.AreaSkuManyWarehouseDO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceDeliveryEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;

import java.util.List;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/8/2 13:49<br/>
 *
 * <AUTHOR> />
 */
public interface FenceRepository {

    /**
     * 分页查询围栏列表
     * @param fencePageQuery 查询
     * @return 结果
     */
    PageInfo<FenceEntity> queryPage(FencePageQuery fencePageQuery);

    /**
     * 根据运营区域编号查询有效的
     * @param areaNo 运营区域编号
     * @return 围栏结果
     */
    List<FenceEntity> queryValidFenceByAreaNo(Integer areaNo);

    /**
     * 根据围栏组查询城配仓编号集合
     *
     * @param packId 组
     * @return 城配仓编号集合
     */
    List<Integer> queryValidStoreNoByPackId(Integer packId);

    /**
     * 根据围栏ID查询围栏
     * @param fenceId 围栏ID
     * @return 围栏信息
     */
    FenceEntity queryById(Integer fenceId);

    /**
     * 根据围栏ID查询围栏配送信息
     * @param fenceId 围栏ID
     * @return 围栏信息
     */
    FenceDeliveryEntity queryDeliveryById(Integer fenceId);

    /**
     * 查询围栏集合
     * @param fenceQuery 查询
     * @return 围栏集合
     */
    List<FenceEntity> queryList(FenceQuery fenceQuery);
    /**
     * 更新围栏信息
     * @param fenceEntity 围栏实体
     * @return 影响条数
     */
    int update(FenceEntity fenceEntity);

    /**
     * 根据城配仓编号获取所有打包数据 老模型迁移的逻辑。。有点坑的
     */
    List<Integer> queryPackAllByStoreNo(Integer storeNo);

    /**
     * 查询主表和关联的所有信息
     * @param fenceId 委托单ID
     * @return 围栏信息
     */
    FenceEntity queryDetail(Integer fenceId);

    /**
     * 获取最大打包ID
     * @return 结果
     */
    Integer queryMaxPackId();

    /**
     * 保存或更新围栏信息
     * @param fenceEntity 围栏实体
     */
    void saveOrUpdate(FenceEntity fenceEntity);

    /**
     * 改变围栏状态
     * @param fenceEntity 围栏实体
     */
    void changeStatus(FenceEntity fenceEntity);

    /**
     * 查询所有围栏
     * @param status 状态集合
     * @return 结果
     */
    List<AdCodeMsgEntity> queryAllFenceAreas(List<Integer> status);

    /**
     * 根据城配仓编号获取所有围栏信息
     * @param storeNoList 城配仓编号集合
     * @return 围栏集合
     */
    List<FenceEntity> queryByStoreNoList(List<Integer> storeNoList);

    /**
     * 根据城配仓编号获取所有围栏信息
     * @param storeNoList 城配仓编号集合
     * @return 围栏集合
     */
    List<AddrDO> queryAddrDOByStoreNoList(List<Integer> storeNoList);

    /**
     * 查询所有的有效的围栏信息
     * @return 围栏集合
     */
    List<AddrDO> queryAddrDOAll();

    /**
     * 查询城市和区县的围栏信息
     * @param city 城市
     * @param area 区县
     * @return
     */
    FenceEntity queryFenceByAddr(String city, String area);

    /**
     * 查询同一个运营区域存在多个仓库的sku
     * @param areaNo 运营区域编号
     * @return 多个仓库的sku集合
     */
    List<AreaSkuManyWarehouseDO> queryAreaSkuManyWarehouse(Integer areaNo);

    /**
     * 查询围栏集合 带有区域信息
     * @param fenceQuery 查询
     * @return 围栏集合
     */
    List<FenceEntity> queryListWithArea(FenceQuery fenceQuery);

    /**
     * 根据区域ID集合查询区域对应的围栏信息
     * @param currentDBAdCodeMsgIds 区域ID集合
     * @return 区域对应的围栏信息
     */
    Map<Integer, FenceEntity> queryByAdCodeMsgIds(List<Integer> currentDBAdCodeMsgIds);
}
