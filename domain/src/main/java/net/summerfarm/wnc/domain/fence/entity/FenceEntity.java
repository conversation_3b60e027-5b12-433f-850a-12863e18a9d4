package net.summerfarm.wnc.domain.fence.entity;

import lombok.Data;
import net.summerfarm.wnc.common.enums.FenceDeliveryEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/7 19:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class FenceEntity {

    /**
     * 围栏ID
     */
    private Integer id;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 运营服务区域编号
     */
    private Integer areaNo;

//    /**
//     * 状态 0正常 1失效
//     */
//    private Integer status;

    /**
     * 状态，0：正常，1：失效，3：暂停
     */
    private FenceEnums.Status status;

//    /**
//     * 添加时间
//     */
//    private LocalDateTime addTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

//    /**
//     * 最后操作人ID
//     */
//    private Integer adminId;

    /**
     * 更新人ID
     */
    private Integer updaterId;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 打包ID
     */
    private Integer packId;

//    /**
//     * 0 新建 1 拆分
//     */
//    private Integer type;

    /**
     * 类型，0：新建围栏，1：围栏拆分 2：自定义围栏
     */
    private FenceEnums.Type type;

    /**
     * 围栏区域信息
     */
    private List<AdCodeMsgEntity> adCodeMsgEntities;

    /**
     * 配送周期信息
     */
    private FenceDeliveryEntity fenceDeliveryEntity;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 行政城市名称
     */
    private String cityName;

    /**
     * 下单渠道类型 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户
     */
    private String orderChannelType;

    /**
     * 下单渠道白名单配置
     */
    private List<FenceChannelBusinessWhiteConfigEntity>  fenceChannelBusinessWhiteConfigEntities;
    /**
     * 创建围栏
     * @param creatorId 创建人ID
     */
    public void create(Integer creatorId) {
        this.status = FenceEnums.Status.VALID;
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
        this.updaterId = creatorId;
        this.fenceDeliveryEntity.create(creatorId);
        if (!CollectionUtils.isEmpty(this.adCodeMsgEntities)){
            this.adCodeMsgEntities.forEach(AdCodeMsgEntity::create);
        }
    }

    public boolean isValid() {
        return Objects.equals(this.status, FenceEnums.Status.VALID);
    }
    public boolean isStop() {
        return Objects.equals(this.status, FenceEnums.Status.STOP);
    }
    public boolean isInValid() {
        return Objects.equals(this.status, FenceEnums.Status.INVALID);
    }

    public FenceEntity update(FenceEntity editedFenceEntity, Integer updaterId) {
        FenceEntity update = new FenceEntity();
        update.setId(this.id);
        update.setUpdaterId(updaterId);
        update.setUpdateTime(LocalDateTime.now());
        List<AdCodeMsgEntity> newAdCodeMsgEntities = editedFenceEntity.getAdCodeMsgEntities();
        if (!CollectionUtils.isEmpty(newAdCodeMsgEntities)){
            newAdCodeMsgEntities.forEach(e -> e.create(this.status));
            update.setAdCodeMsgEntities(newAdCodeMsgEntities);
        }
        if (this.isDeliveryFrequentChanged(editedFenceEntity)) {
            update.setFenceDeliveryEntity(this.fenceDeliveryEntity.update(editedFenceEntity.getFenceDeliveryEntity(), updaterId));
        }
        update.setOrderChannelType(editedFenceEntity.getOrderChannelType());
        return update;
    }

    public boolean isDeliveryFrequentChanged(FenceEntity editedFenceEntity) {
        FenceDeliveryEntity editedFenceDeliveryEntity = editedFenceEntity.getFenceDeliveryEntity();
        boolean isMethodChanged = !Objects.equals(this.fenceDeliveryEntity.getFrequentMethod(), editedFenceDeliveryEntity.getFrequentMethod());
        if (isMethodChanged) {
            return true;
        }
        boolean isNextDeliveryDateChanged = !Objects.equals(this.fenceDeliveryEntity.getNextDeliveryDate(), editedFenceDeliveryEntity.getNextDeliveryDate());
        if (isNextDeliveryDateChanged) {
            return true;
        }
        if (Objects.equals(this.fenceDeliveryEntity.getFrequentMethod(), FenceDeliveryEnums.FrequentMethod.WEEK_CALC.getValue())) {
            return !Objects.equals(this.fenceDeliveryEntity.getDeliveryFrequent(), editedFenceDeliveryEntity.getDeliveryFrequent());
        }
        if (Objects.equals(this.fenceDeliveryEntity.getFrequentMethod(), FenceDeliveryEnums.FrequentMethod.INTERVAL_CALC.getValue())){

            return !Objects.equals(this.fenceDeliveryEntity.getBeginCalculateDate(), editedFenceDeliveryEntity.getBeginCalculateDate())
                    || !Objects.equals(this.fenceDeliveryEntity.getDeliveryFrequentInterval(), editedFenceDeliveryEntity.getDeliveryFrequentInterval());
        }
        return false;
    }

    public FenceEntity changeStatus(FenceEnums.Status editedStatus, Integer updaterId) {
        FenceEntity update = new FenceEntity();
        update.setId(this.id);
        update.setStatus(editedStatus);
        //修改围栏状态 同步修改围栏下的区域状态
        update.setAdCodeMsgEntities(this.adCodeMsgEntities.stream().map(e -> {
            AdCodeMsgEntity adCodeMsgEntity = new AdCodeMsgEntity();
            adCodeMsgEntity.setId(e.getId());
            adCodeMsgEntity.setStatus(editedStatus.getValue());
            adCodeMsgEntity.setUpdateTime(LocalDateTime.now());
            return adCodeMsgEntity;
        }).collect(Collectors.toList()));
        update.setUpdaterId(updaterId);
        update.setUpdateTime(LocalDateTime.now());
        return update;
    }

    /**
     * 校验客户类型和订单类型围栏是否支持
     * @param customerType 客户类型
     * @return 结果
     */
    public boolean checkOrderChannelType(String customerType) {
        if(StringUtils.isBlank(customerType)){
            throw new BizException("客户类型不能为空");
        }
        //不包含则不支持配送
        return this.getOrderChannelType().contains(customerType);
    }
}
