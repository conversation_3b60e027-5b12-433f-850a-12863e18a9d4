package net.summerfarm.wnc.domain.fence.service;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 *
 * @Title: 围栏变更执行记录表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Slf4j
@Service
public class WncFenceChangeRecordsCommandDomainService {


    @Autowired
    private WncFenceChangeRecordsCommandRepository wncFenceChangeRecordsCommandRepository;
    @Autowired
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Autowired
    private WncFenceAreaChangeRecordsCommandDomainService wncFenceAreaChangeRecordsCommandDomainService;
    @Autowired
    private WncFenceAreaChangeRecordsCommandRepository wncFenceAreaChangeRecordsCommandRepository;



    public WncFenceChangeRecordsEntity insert(WncFenceChangeRecordsCommandParam param) {
        return wncFenceChangeRecordsCommandRepository.insertSelective(param);
    }


    public int update(WncFenceChangeRecordsCommandParam param) {
        return wncFenceChangeRecordsCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wncFenceChangeRecordsCommandRepository.remove(id);
    }

    /**
     * 创建围栏区域记录
     *
     * @param fenceChangeRecords 围栏变更记录列表
     */
    public void createFenceAreaRecord(List<WncFenceChangeRecordsCommandParam> fenceChangeRecords) {

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            return;
        }

        for (WncFenceChangeRecordsCommandParam fenceChangeRecord : fenceChangeRecords) {
            WncFenceChangeRecordsEntity savedEntity = wncFenceChangeRecordsCommandRepository.insertSelective(fenceChangeRecord);

            List<WncFenceAreaChangeRecordsCommandParam> fenceAreaChangeRecords = fenceChangeRecord.getFenceAreaChangeRecords();
            fenceAreaChangeRecords.forEach(areaRecord -> {
                areaRecord.setFenceChangeId(savedEntity.getId());
                wncFenceAreaChangeRecordsCommandRepository.insertSelective(areaRecord);
            });
        }

    }

}
