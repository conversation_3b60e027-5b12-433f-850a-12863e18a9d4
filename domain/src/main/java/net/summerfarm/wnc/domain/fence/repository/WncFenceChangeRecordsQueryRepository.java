package net.summerfarm.wnc.domain.fence.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;

import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;



/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncFenceChangeRecordsQueryRepository {

    PageInfo<WncFenceChangeRecordsEntity> getPage(WncFenceChangeRecordsQueryParam param);

    WncFenceChangeRecordsEntity selectById(Long id);

    List<WncFenceChangeRecordsEntity> selectByCondition(WncFenceChangeRecordsQueryParam param);

    /**
     * 根据变更批次号集合查询围栏变更记录
     * @param changeBatchNoList 变更批次号集合
     * @return 围栏变更记录集合
     */
    List<WncFenceChangeRecordsEntity> selectByChangeBatchNoList(List<String> changeBatchNoList);

    /**
     * 根据变更批次号查询围栏变更记录，包含区域信息
     * @param changeBatchNo 变更批次号
     * @return 围栏变更记录集合
     */
    List<WncFenceChangeRecordsEntity> selectWithAreaByChangeBatchNo(String changeBatchNo);

    /**
     * 根据切仓任务ID查询围栏变更记录，包含区域信息
     * @param fenceChangeId 切仓任务ID
     * @return 围栏变更记录集合
     */
    List<WncFenceChangeRecordsEntity> selectWithAreaByFenceChangeId(Long fenceChangeId);
}