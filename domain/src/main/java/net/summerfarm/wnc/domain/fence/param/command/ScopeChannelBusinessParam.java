package net.summerfarm.wnc.domain.fence.param.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ScopeChannelBusinessParam {

	/**
	 * 租户ID
	 */
	private Long tenantId;

	/**
	 * 作用域下的业务ID 0是全部
	 */
	@NotBlank(message = "作用域下的业务ID不能为空")
	private String scopeChannelBusinessId;


	/**
	 * 作用域下的业务名称
	 */
	@NotBlank(message = "作用域下的业务名称不能为空")
	private String scopeChannelBusinessName;
}