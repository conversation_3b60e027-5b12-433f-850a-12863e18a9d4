package net.summerfarm.wnc.domain.fence.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Data
public class WncFenceAreaChangeRecordsCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 围栏变更记录表ID
	 */
	private Long fenceChangeId;

	/**
	 * 围栏ID
	 */
	private Integer fenceId;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 区域
	 */
	private String area;

	/**
	 * 地区ID
	 */
	private Integer adCodeMsgId;

	/**
	 * 自定义区域名称
	 */
	private String customAreaName;

	/**
	 * 详情
	 */
	private String adCodeMsgDetail;

	/**
	 * 坐标POI图形
	 */
	private String geoShape;

	/**
	 * 围栏变更阶段 0变革前阶段 1变更后阶段 2结束时阶段
	 */
	private Integer fenceChangeStage;

	/**
	 * 区域绘制类型 0已绘制 1未绘制（其他区域）
	 */
	private Integer areaDrawType;
	

	
}