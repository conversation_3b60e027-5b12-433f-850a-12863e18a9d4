package net.summerfarm.wnc.domain.fence;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.config.obj.PopCityAreaOperatingAreaMappingObj;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.enums.*;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.common.query.fence.DeliveryFenceDateQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.util.PopConfigUtil;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.closeTime.CloseTimeConfigQueryDomainService;
import net.summerfarm.wnc.domain.closeTime.param.CloseTimeAreaConfigQueryParam;
import net.summerfarm.wnc.domain.config.entity.WncStockingTimeConfigEntity;
import net.summerfarm.wnc.domain.config.param.query.WncStockingTimeConfigQueryParam;
import net.summerfarm.wnc.domain.config.repository.ContactConfigRepository;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.config.repository.WncStockingTimeConfigQueryRepository;
import net.summerfarm.wnc.domain.deliveryRule.entity.ContactDeliveryRuleEntity;
import net.summerfarm.wnc.domain.fence.entity.*;
import net.summerfarm.wnc.domain.fence.param.CloseTimeQueryParam;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.FenceChannelBusinessWhiteConfigQueryDomainService;
import net.summerfarm.wnc.domain.fence.valueObject.FenceBigCustomerValueObject;
import net.summerfarm.wnc.domain.warehouse.entity.ConfigEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description:配送围栏领域服务
 * date: 2023/6/8 14:09
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeliveryFenceDomainService {

	private final DeliveryFenceRepository deliveryFenceRepository;
	private final ConfigRepository configRepository;
	/**
	 * saas配送时间逻辑开关,false表示saas销售订单配送时间跟随鲜沐侧销售订单时间。true表示默认配送时间
	 */
	private final WncConfig wncConfig;
	private final OutLandMerchantDomainService outLandMerchantDomainService;
	private final CloseTimeConfigQueryDomainService closeTimeQueryDomainService;
	private final ContactConfigRepository contactConfigRepository;
	private final FenceChannelBusinessWhiteConfigQueryDomainService fenceChannelBusinessWhiteConfigQueryDomainService;
	private final PopConfigUtil popConfigUtil;
	private final WncStockingTimeConfigQueryRepository wncStockingTimeConfigQueryRepository;
	private final AdCodeMsgRepository adCodeMsgRepository;
	private final FenceRepository fenceRepository;
	private final CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;

	/**
	 * 查询截单时间
	 *
	 * @param query 查询条件
	 * @return 截单时间
	 */
	public LocalTime queryCloseTime(CloseTimeQueryParam query) {
		log.info("queryCloseTime 查询截单时间:{}", JSON.toJSONString(query));
		SourceEnum sourceEnum = query.getSource();
		Long tenantId = query.getTenantId();
		String city = query.getCity();
		String area = query.getArea();
		Long merchantId = query.getMerchantId();
		Integer storeNo = null;
		Long contactId = query.getContactId();

		if (tenantId != null) {
			//优先查询客户自定义截单时间
			if (SourceEnum.getSaasSource().contains(sourceEnum)) {
				LocalTime configuredCloseTime = closeTimeQueryDomainService.queryConfiguredCloseTime(CloseTimeAreaConfigQueryParam.builder()
						.tenantId(tenantId)
						.city(city)
						.area(area).build());
				log.info("客户自定义截单时间:{}",configuredCloseTime);
				if (configuredCloseTime != null) {
					return configuredCloseTime;
				}
				List<Long> chageeTenantIdList = wncConfig.queryChageeTenantIdList();

				// 霸王茶姬租户需要兜底的截单时间
				if (chageeTenantIdList.contains(tenantId)) {
					ConfigEntity configEntity = configRepository.queryByKey(AppConsts.ConfigKey.CHAGEE_TENANT_ID_CLOSE_TIME);
					if (configEntity != null && !StringUtils.isBlank(configEntity.getValue())){
						return LocalTime.parse(configEntity.getValue());
					}
				}
			}
		}
		//查询指定的城配仓
		if (contactId != null) {
			ContactConfigEntity contactConfigEntity = null;
			if(tenantId != null){
				contactConfigEntity = contactConfigRepository.queryByUk(ContactConfigEnums.Source.getSourceByTenantId(tenantId), contactId);
			}else if(sourceEnum != null){
				//保险租户ID没有给的场景
				contactConfigEntity = contactConfigRepository.queryByUk(ContactConfigEnums.Source.getSourceByOrderSource(sourceEnum), contactId);
			}
			storeNo = contactConfigEntity != null ? contactConfigEntity.getStoreNo() : null;
			log.info("客户指定城配仓:{}",storeNo);
		}

		LocalTime bigCustomerCloseTime = null;

		if (merchantId != null && SourceEnum.getXmSource().contains(sourceEnum)) {
			//查询大客户的截单时间
			FenceBigCustomerValueObject fenceBigCustomer = outLandMerchantDomainService.queryBigCustomerCloseTime(merchantId);
			if (fenceBigCustomer != null && fenceBigCustomer.isBigCustomerFlag()) {
				bigCustomerCloseTime = fenceBigCustomer.getCloseOrderTime();
			}
			log.info("查询大客户的截单时间:{}",JSON.toJSONString(fenceBigCustomer));
		}

		if (BooleanUtils.isTrue(wncConfig.getOpenDefaultSaasDeliveryTime()) && Objects.equals(SourceEnum.SAAS_MALL, sourceEnum)) {
			return wncConfig.defaultSaasOrderTime();
		}

		// 如果是POP来源则获取POP截单时间
		LocalTime popCloseTime = null;
		if (SourceEnum.POP_MALL.equals(sourceEnum) || SourceEnum.POP_AFTER_SALE.equals(sourceEnum)) {
			PopCityAreaOperatingAreaMappingObj matchedPopConfig = popConfigUtil.queryMatchedPopConfig(city, area);
			if (matchedPopConfig != null) {
				if (PopFulfillmentWayEnum.POP_POP_FULFILLMENT_WAY.getValue().equals(matchedPopConfig.getPopFulfillmentWay())) {
					popCloseTime = matchedPopConfig.getCloseTime();
					log.info("POP专属城配仓履约:{},截单时间:{}", matchedPopConfig.getStoreNo(), popCloseTime);
				} else {
					if(query.getIsXmPopSkuType() == null){
						popCloseTime = matchedPopConfig.getCloseTime();
					}else if (!query.getIsXmPopSkuType()) {
						// 纯POP品,取配置的截单时间
						popCloseTime = matchedPopConfig.getCloseTime();
						log.info("纯POP品，非POP专属城配仓截单时间:{}", popCloseTime);
					} else {
						log.info("鲜沐POP品，走城市区域的截单时间");
					}
				}
			}
		}

		if (storeNo != null) {
			LocalTime storeCloseTime = deliveryFenceRepository.queryStoreCloseTime(storeNo);
			log.info("指定城配仓截单时间:{}",storeCloseTime);
			if (popCloseTime != null) {
				storeCloseTime = popCloseTime;
			}
			return bigCustomerCloseTime != null ? (storeCloseTime.compareTo(bigCustomerCloseTime) > 0 ? bigCustomerCloseTime : storeCloseTime) : storeCloseTime;
		}

		if (configRepository.queryNoAreaCity().contains(city)) {
			area = null;
		}

		LocalTime storeCloseTime = deliveryFenceRepository.queryCloseTime(city, area);
		log.info("省市区城配仓截单时间:{}",storeCloseTime);
		if (popCloseTime != null) {
			storeCloseTime = popCloseTime;
		}
		return bigCustomerCloseTime != null ? (storeCloseTime.compareTo(bigCustomerCloseTime) > 0 ? bigCustomerCloseTime : storeCloseTime) : storeCloseTime;
	}


	public FenceEntity queryDeliveryFence(FenceQuery fenceQuery) {
		//根据配置查找没有区域的城市
		List<String> noAreaCityList = configRepository.queryNoAreaCity();
		if (!noAreaCityList.contains(fenceQuery.getCity())) {
			if (StrUtil.isBlank(fenceQuery.getArea())) {
				throw new BizException("区域不能为空");
			}
		}
		return deliveryFenceRepository.queryOneFenceByProvinceCityArea(fenceQuery);
	}

	/**
	 * 处理围栏相关的配送规则
	 * @param outLandContactEntity 联系人信息
	 * @param deliveryFenceDateQuery 查询信息
	 * @return
	 */
	public DeliveryFenceEntity handleInstallDeliveryFence(OutLandContactEntity outLandContactEntity, DeliveryFenceDateQuery deliveryFenceDateQuery) {

		DeliveryFenceEntity deliveryFenceEntityNew = this.queryFrequentRule(outLandContactEntity);
		//查询围栏相关信息
		DeliveryFenceEntity deliveryFenceEntity = deliveryFenceRepository.queryFrequentRule(outLandContactEntity);
		//联系人配送周期
		deliveryFenceEntity.setContactDeliveryRuleEntity(outLandContactEntity.getContactDeliveryRuleEntity());
		//鲜沐的订单来源
		if(SourceEnum.getXmSource().contains(deliveryFenceDateQuery.getOrderSourceEnum())){
			//查询是否是大客户
			FenceBigCustomerValueObject fenceBigCustomer = outLandMerchantDomainService.queryBigCustomerCloseTime(deliveryFenceDateQuery.getMerchantId());
			deliveryFenceEntity.setXmBigCustomerFlag(fenceBigCustomer.isBigCustomerFlag());
			deliveryFenceEntity.setXmBigAdminId(fenceBigCustomer.getXmBigAdminId());
		}

		// 查询渠道白名单配置信息
		Integer fenceId = deliveryFenceEntity.getFenceEntity() != null ? deliveryFenceEntity.getFenceEntity().getId() : null;
		List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntities = fenceChannelBusinessWhiteConfigQueryDomainService.queryChannelBusinessWhiteConfig(
				fenceId,
				deliveryFenceDateQuery.getTenantId(),
				deliveryFenceEntity.getXmBigAdminId());
		deliveryFenceEntity.setFenceChannelBusinessWhiteConfigEntities(fenceChannelBusinessWhiteConfigEntities);

		//查询截单时间
		LocalTime closeTime = this.queryCloseTime(CloseTimeQueryParam.builder()
				.city(outLandContactEntity.getCity())
				.area(outLandContactEntity.getArea())
				.source(deliveryFenceDateQuery.getOrderSourceEnum())
				.contactId(outLandContactEntity.getContactId())
				.tenantId(outLandContactEntity.getTenantId())
				.merchantId(deliveryFenceDateQuery.getMerchantId())
				.build());
		deliveryFenceEntity.setCloseTime(closeTime);

		// 获取可加单时长
		List<Long> chageeTenantIdList = wncConfig.queryChageeTenantIdList();
		if (chageeTenantIdList.contains(outLandContactEntity.getTenantId())) {
			ConfigEntity configEntity = configRepository.queryByKey(AppConsts.ConfigKey.CHAGEE_ADD_ORDER_TIME);
			if (configEntity != null && StrUtil.isNotBlank(configEntity.getValue())) {
				deliveryFenceEntity.setCouldAddOrderTimeHours(new BigDecimal(configEntity.getValue()));
			} else {
				deliveryFenceEntity.setCouldAddOrderTimeHours(new BigDecimal(0));
			}
		}

		log.info("查询联系人信息:{},查询围栏配送相关信息:{}", JSON.toJSONString(outLandContactEntity),JSON.toJSONString(deliveryFenceEntity));
		if (closeTime == null) {
			throw new BizException("此地区不支持配送");
		}

		// 获取备货时长
		Integer stockingTime = this.queryStockingTime(outLandContactEntity, deliveryFenceDateQuery, deliveryFenceEntity, chageeTenantIdList);
		deliveryFenceEntity.setStockingTime(stockingTime);

		return deliveryFenceEntity;
	}

	private DeliveryFenceEntity queryFrequentRule(OutLandContactEntity outLandContactEntity) {
		DeliveryFenceEntity deliveryFenceEntity = new DeliveryFenceEntity();
		//判断客户是否有指定城配仓
		ContactConfigEntity contactConfigEntity = outLandContactEntity.getContactConfigEntity();
		ContactDeliveryRuleEntity contactDeliveryRuleEntity = outLandContactEntity.getContactDeliveryRuleEntity();
		//指定了城配仓和配送周期
		if(contactConfigEntity != null && contactDeliveryRuleEntity != null){
			//将客户的配送规则赋值给配送规则实体
			FenceDeliveryEntity fenceDeliveryEntity = new FenceDeliveryEntity();
			fenceDeliveryEntity.setFrequentMethod(contactDeliveryRuleEntity.getFrequentMethod());
			fenceDeliveryEntity.setDeliveryFrequent(contactDeliveryRuleEntity.getWeekDeliveryFrequent());
			fenceDeliveryEntity.setDeliveryFrequentInterval(contactDeliveryRuleEntity.getDeliveryFrequentInterval());
			fenceDeliveryEntity.setBeginCalculateDate(contactDeliveryRuleEntity.getBeginCalculateDate());
			fenceDeliveryEntity.setNextDeliveryDate(contactDeliveryRuleEntity.getBeginCalculateDate());
			deliveryFenceEntity.setFenceDeliveryEntity(fenceDeliveryEntity);
			StopDeliveryEntity stopDeliveryEntity = this.queryStopDelivery(contactConfigEntity.getStoreNo());
			deliveryFenceEntity.setStopDeliveryEntitys(stopDeliveryEntity != null ? Collections.singletonList(stopDeliveryEntity) : Collections.emptyList());
			deliveryFenceEntity.setWarehouseLogisticsCenterEntity(warehouseLogisticsCenterRepository.queryByUk(contactConfigEntity.getStoreNo()));
			return deliveryFenceEntity;
		}
		//查询围栏
		FenceEntity fence = this.queryValidFenceByAddressWithPoi(outLandContactEntity.getCity(), outLandContactEntity.getArea(), outLandContactEntity.getPoiNote());
		if (fence != null && Objects.isNull(outLandContactEntity.getStoreNo())) {
			outLandContactEntity.setStoreNo(fence.getStoreNo());
		}
		deliveryFenceEntity.setFenceEntity(fence);
		//查询围栏配送周期
		deliveryFenceEntity.setFenceDeliveryEntity(FenceConverter.fenceDelivery2Entity(queryFenceDelivery(fence)));
		//停运
		StopDeliveryEntity stopDeliveryEntity = null;
		if(contactConfigEntity != null){
			stopDeliveryEntity = this.queryStopDelivery(contactConfigEntity.getStoreNo());
			deliveryFenceEntity.setWarehouseLogisticsCenterEntity(warehouseLogisticsCenterRepository.queryByUk(contactConfigEntity.getStoreNo()));
		}else{
			stopDeliveryEntity = this.queryStopDelivery(fence);
			deliveryFenceEntity.setWarehouseLogisticsCenterEntity(fence != null ? warehouseLogisticsCenterRepository.queryByUk(fence.getStoreNo()) : null);
		}
		deliveryFenceEntity.setStopDeliveryEntitys(stopDeliveryEntity != null ? Collections.singletonList(stopDeliveryEntity) : Collections.emptyList());
		return deliveryFenceEntity;
	}

	/**
	 * 查询备货时长
	 * @param outLandContactEntity 联系人信息
	 * @param deliveryFenceDateQuery 查询信息
	 * @param deliveryFenceEntity 配送规则信息
	 * @param chageeTenantIdList 霸王茶姬租户id
	 * @return 备货时长
	 */
	private Integer queryStockingTime(OutLandContactEntity outLandContactEntity, DeliveryFenceDateQuery deliveryFenceDateQuery, DeliveryFenceEntity deliveryFenceEntity, List<Long> chageeTenantIdList) {
		// 判断租户是否是霸王茶姬、需要考虑备货时长
		boolean noNeedToStockUp = deliveryFenceDateQuery.getNoNeedToStockUp() != null && deliveryFenceDateQuery.getNoNeedToStockUp();
		if (chageeTenantIdList.contains(outLandContactEntity.getTenantId()) && !noNeedToStockUp) {
			Integer isEveryDayFlag = deliveryFenceEntity.handleEveryDaySendFlag();
			WarehouseLogisticsCenterEntity storeEntity = deliveryFenceEntity.getWarehouseLogisticsCenterEntity();

			if (storeEntity != null) {
				WncStockingTimeConfigQueryParam wncStockingTimeConfigQueryParam = WncStockingTimeConfigQueryParam.builder()
						.tenantId(deliveryFenceDateQuery.getTenantId())
						.businessNo(storeEntity.getStoreNo())
						.fulfillmentType(storeEntity.getFulfillmentType().getValue())
						.deliveryRules(isEveryDayFlag)
						.build();

				log.info("查询备货时长条件信息:{}", JSON.toJSONString(wncStockingTimeConfigQueryParam));
				List<WncStockingTimeConfigEntity> wncStockingTimeConfigEntities = wncStockingTimeConfigQueryRepository.selectByCondition(wncStockingTimeConfigQueryParam);
				if (!CollectionUtils.isEmpty(wncStockingTimeConfigEntities)) {
					WncStockingTimeConfigEntity wncStockingTimeConfigEntity = wncStockingTimeConfigEntities.get(0);
					log.info("查询备货时长信息:{}", JSON.toJSONString(wncStockingTimeConfigEntity));
					return wncStockingTimeConfigEntity.getStockingTime();
				}

				// 为空时，默认为1天
				return 1;
			}
		}

		return 0;
	}

	/**
	 * 找出首配日期
	 *
	 * @param deliveryFenceEntity    围栏信息
	 * @param deliveryFenceDateQuery 查询条件
	 * @return
	 */
	public LocalDate findFirstDeliveryDate(DeliveryFenceEntity deliveryFenceEntity, DeliveryFenceDateQuery deliveryFenceDateQuery) {
		List<LocalDate> wantDeliveryTime = deliveryFenceDateQuery.getWantDeliveryTime();
		LocalDateTime orderTime = deliveryFenceDateQuery.getOrderTime();
		boolean addOrderFlag = deliveryFenceDateQuery.getAddOrderFlag() != null && deliveryFenceDateQuery.getAddOrderFlag();

		List<Long> chageeTenantIdList = wncConfig.queryChageeTenantIdList();
		// 加单判断&&租户判断
		if(addOrderFlag && chageeTenantIdList.contains(deliveryFenceDateQuery.getTenantId())){
			BigDecimal couldAddOrderTimeHours = deliveryFenceEntity.getCouldAddOrderTimeHours();
			if (couldAddOrderTimeHours != null) {
				orderTime = orderTime.minusMinutes(couldAddOrderTimeHours.multiply(BigDecimal.valueOf(60)).longValue());
			}
		}

		//截单日期的判断
		LocalDateTime closingTime = deliveryFenceEntity.buildOrderTimeCloseTime(orderTime);
		//配送时间的判断
		LocalDateTime deliveryTime = null;
		//普通订单处理
		if(CollectionUtils.isEmpty(wantDeliveryTime)){
			if (orderTime.compareTo(closingTime) <= 0) {
				deliveryTime = closingTime.plusDays(1);
			} else {
				if (orderTime.toLocalDate().compareTo(closingTime.toLocalDate()) > 0) {
					deliveryTime = orderTime;
				} else {
					deliveryTime = closingTime.plusDays(2);
				}
			}
		}else{
			//配送时间段
			if (orderTime.compareTo(closingTime) <= 0 && LocalDateTime.now().compareTo(closingTime) <= 0) {
				deliveryTime = closingTime.plusDays(1);
			}else{
				deliveryTime = closingTime.plusDays(2);
				try {
					// 当日截单时间大于订单当日的截单时间
					LocalDateTime currentCloseTime = LocalDateTime.of(LocalDate.now(), deliveryFenceEntity.getCloseTime());
					if (currentCloseTime.compareTo(closingTime) > 0) {
						log.info("记录线上使用场景情况，时间段查询配送日期当前加了两天，当日截单时间:{}，订单截单时间:{},订单时间:{},配送日期时间:{}",currentCloseTime, closingTime, orderTime, deliveryTime);
					}
				} catch (Exception e) {
					log.error("记录日志截单时间异常");
				}
			}

		}

		//判断首配日期
		if (deliveryFenceEntity.getFenceDeliveryEntity() != null) {
			deliveryTime = deliveryFenceEntity.getFenceDeliveryEntity().fristDateJudge(deliveryTime);
		}

		// 备货时长处理
		Integer stockingTime = deliveryFenceEntity.getStockingTime() == null ? 0 : deliveryFenceEntity.getStockingTime();
		deliveryTime = deliveryTime.plusDays(stockingTime);

		return deliveryTime.toLocalDate();
	}


	/**
	 * 根据地址查询有效的围栏
	 *
	 * @param city 城市
	 * @param area 区域
	 * @param poi  POI
	 * @return 有效围栏
	 */
	public FenceEntity queryValidFenceByAddressWithPoi(String city, String area, String poi) {
		if (StringUtils.isEmpty(city) && StringUtils.isEmpty(area)) {
			return null;
		}
		List<String> noAreaCityList = configRepository.queryNoAreaCity();
		if (noAreaCityList.contains(area)) {
			area = null;
		}

		// 根据城市、区域查询有效的区域信息
		List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgRepository.queryList(AdCodeMsgQuery.builder()
				.city(city)
				.area(area)
				.status(AdCodeMsgEnums.Status.VALID.getValue())
				.build());

		if (CollectionUtils.isEmpty(adCodeMsgEntities)) {
			return null;
		}
		List<Integer> areaTypeList = adCodeMsgEntities.stream().map(AdCodeMsgEntity::getAreaType).distinct().collect(Collectors.toList());
		if (areaTypeList.size() > 1) {
			log.error("\n city:{},area:{},区域类型存在自定义区域和普通区域,已走普通区域逻辑\n", city, area);
		}

		if (areaTypeList.contains(AdCodeMsgEnums.AreaType.CUSTOM_AREA.getValue()) && areaTypeList.size() == 1) {
			// 自定义区域走ES匹配
			List<Integer> adCodeMsgIds = adCodeMsgEntities.stream().map(AdCodeMsgEntity::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
			if (CollectionUtils.isEmpty(adCodeMsgIds)) {
				return null;
			}
			Integer matchAdCodeMsgId = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(adCodeMsgIds, poi);
			if (matchAdCodeMsgId == null) {
				return null;
			}
			Map<Integer, Integer> adCodeMsgIdToFenceIdMap = adCodeMsgEntities.stream().collect(Collectors.toMap(AdCodeMsgEntity::getId, AdCodeMsgEntity::getFenceId));
			Integer fenceId = adCodeMsgIdToFenceIdMap.get(matchAdCodeMsgId);
			if (fenceId == null) {
				return null;
			}
			FenceEntity fenceEntity = fenceRepository.queryById(fenceId);
			if (fenceEntity == null || !Objects.equals(fenceEntity.getStatus(), FenceEnums.Status.VALID)) {
				return null;
			}
			return fenceEntity;
		} else {
			// 省市区区域走文字匹配,城市区域在普通围栏下只会有一个
			AdCodeMsgEntity adCodeMsgEntity = adCodeMsgEntities.get(0);
			FenceEntity fenceEntity = fenceRepository.queryById(adCodeMsgEntity.getFenceId());
			if (fenceEntity == null || !Objects.equals(fenceEntity.getStatus(), FenceEnums.Status.VALID)) {
				return null;
			}
			return fenceEntity;
		}
	}

}
