package net.summerfarm.wnc.domain.fence;

import net.summerfarm.wnc.domain.fence.entity.AreaEntity;

import java.util.List;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2023/8/2 13:36<br/>
 *
 * <AUTHOR> />
 */
public interface AreaRepository {
    /**
     * 查询状态开放的运营区域
     *
     * @return 运营区域集合
     */
    List<Integer> queryOpenAreaList();

    


    /**
     * 根据城市区域查询运营区域
     *
     * @param city
     * @param area
     * @return
     */
    AreaEntity queryByAddress(String city, String area);

    /**
     * 根据UK查询运营区域
     * @param areaNo 运营区域编号
     * @return 运营区域信息
     */
    AreaEntity queryByUk(Integer areaNo);

    /**
     * 根据运营区域编号查询运营区域
     * @param areaNos 运营区域编号
     * @return 运营区域信息
     */
    List<AreaEntity> queryByAreaNos(List<Integer> areaNos);

    /**
     * 根据运营区域编号查询运营区域名称
     * @param areaNos 运营区域编号
     * @return 运营区域信息
     */
    Map<Integer, String> queryAreaNoToNameMapByAreaNos(List<Integer> areaNos);
}
