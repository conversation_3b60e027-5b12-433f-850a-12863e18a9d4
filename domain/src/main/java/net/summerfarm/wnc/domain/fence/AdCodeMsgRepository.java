package net.summerfarm.wnc.domain.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgByStoreNoAddrStatusQuery;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.domain.fence.dataObject.AdCodeMsgFenceStoreDO;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-08-14
 **/
public interface AdCodeMsgRepository {
	/**
	 * 根据城市查询区域信息
	 *
	 * @param cityList
	 * @return
	 */
	List<AdCodeMsgEntity> queryByCityList(List<String> cityList);

	/**
	 * 查询区域信息
	 *
	 * @param adCodeMsgQuery 查询
	 * @return 区域信息集合
	 */
	List<AdCodeMsgEntity> queryList(AdCodeMsgQuery adCodeMsgQuery);

	/**
	 * 更新区域信息
	 * @param adCodeMsgEntity 区域实体
	 * @return 影响条数
	 */
	int update(AdCodeMsgEntity adCodeMsgEntity);

	/**
	 * 根据城市和非围栏ID查询区域信息
	 * @param city 城市
	 * @param fenceId 围栏ID
	 * @return 区域信息
	 */
    List<AdCodeMsgEntity> queryByCityAndNotFenceId(String city, Integer fenceId);

	/**
	 * 分页查询
	 * @param adCodeMsgQuery 查询
	 */
	PageInfo<AdCodeMsgFenceStoreDO> queryPage(AdCodeMsgQuery adCodeMsgQuery);

	/**
	 * 根据城配仓号、状态、围栏状态、仓配中心状态、城市查询区域信息
	 * @param query 查询状态
	 * @return 结果
	 */
	List<AdCodeMsgEntity> queryAdCodeMsgByStoreNoAddrStatus(AdCodeMsgByStoreNoAddrStatusQuery query);

	/**
	 * 根据ID集合解绑区域并失效
	 * @param ids ID集合
	 * @param status 状态值
	 */
    void unbindAndInvalidAreaByIds(List<Integer> ids, Integer status);

	/**
	 * 保存
	 * @param adCodeMsgDetailList 区域信息集合
	 */
    void save(List<AdCodeMsgEntity> adCodeMsgDetailList);
}
