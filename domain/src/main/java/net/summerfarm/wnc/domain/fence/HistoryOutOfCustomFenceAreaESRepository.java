package net.summerfarm.wnc.domain.fence;

import net.summerfarm.wnc.domain.fence.entity.HistoryOutOfCustomFenceAreaEntity;

import java.util.List;
import java.util.Set;

/**
 * Description: 自定义围栏ES<br/>
 * date: 2023/12/6 15:05<br/>
 *
 * <AUTHOR> />
 */
@Deprecated
public interface HistoryOutOfCustomFenceAreaESRepository {
    /**
     * 根据区域编号查询是否存在
     * @param adCode 区域编码
     * @return 结果
     */
    HistoryOutOfCustomFenceAreaEntity queryByAdCode(String adCode);

    /**
     * 查询已经存在的adCode
     * @param adCodeSet 集合
     * @return adCode结果
     */
    Set<String> queryAdCodeByAdCodes(Set<String> adCodeSet);

    /**
     * 批量保存
     * @param needSaveDataList 集合信息
     */
    void batchSave(List<HistoryOutOfCustomFenceAreaEntity> needSaveDataList);

    /**
     * 创建索引
     */
    void createCustomFenceAreaIndex();

    /**
     * poi匹配围栏信息
     * @param poi POI信息
     * @return 围栏结果
     */
    HistoryOutOfCustomFenceAreaEntity queryPoiFence(String poi);

    /**
     * 保存到ES
     * @param entity 对象
     */
    void save(HistoryOutOfCustomFenceAreaEntity entity);
}
